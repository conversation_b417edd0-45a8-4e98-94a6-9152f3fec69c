"""
编码器配置管理模块
"""

from typing import Dict, Any, List
import logging

class EncoderConfig:
    """编码器配置管理类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 编码器配置
        self.encoders = {
            "h264": {
                "name": "H.264 (AVC)",
                "codec": "libx264",
                "description": "广泛兼容的视频编码标准",
                "presets": ["ultrafast", "superfast", "veryfast", "faster", "fast", "medium", "slow", "slower", "veryslow"],
                "default_preset": "slow",
                "crf_range": (0, 51),
                "default_crf": 18,
                "supports_gpu": True,
                "gpu_codec": "h264_nvenc"
            },
            "h265": {
                "name": "H.265 (HEVC)",
                "codec": "libx265",
                "description": "高效率视频编码，文件更小",
                "presets": ["ultrafast", "superfast", "veryfast", "faster", "fast", "medium", "slow", "slower", "veryslow"],
                "default_preset": "slow",
                "crf_range": (0, 51),
                "default_crf": 20,
                "supports_gpu": True,
                "gpu_codec": "hevc_nvenc"
            },
            "av1": {
                "name": "AV1",
                "codec": "libaom-av1",
                "description": "最新的开源视频编码标准",
                "presets": ["0", "1", "2", "3", "4", "5", "6", "7", "8"],
                "default_preset": "4",
                "crf_range": (0, 63),
                "default_crf": 25,
                "supports_gpu": False,
                "gpu_codec": None
            },
            "vp9": {
                "name": "VP9",
                "codec": "libvpx-vp9",
                "description": "Google开发的开源编码器",
                "presets": ["0", "1", "2", "3", "4", "5"],
                "default_preset": "2",
                "crf_range": (0, 63),
                "default_crf": 23,
                "supports_gpu": False,
                "gpu_codec": None
            }
        }
        
        # 质量预设
        self.quality_presets = {
            "lossless": {
                "name": "无损质量",
                "description": "最高质量，文件最大",
                "crf_offset": -10,
                "preset": "slow"
            },
            "high": {
                "name": "高质量",
                "description": "高质量，适合存档",
                "crf_offset": -5,
                "preset": "slow"
            },
            "medium": {
                "name": "中等质量",
                "description": "平衡质量和大小",
                "crf_offset": 0,
                "preset": "medium"
            },
            "low": {
                "name": "低质量",
                "description": "小文件，快速编码",
                "crf_offset": 5,
                "preset": "fast"
            },
            "web": {
                "name": "网络优化",
                "description": "适合网络传输",
                "crf_offset": 3,
                "preset": "fast"
            }
        }
    
    def get_encoder_list(self) -> List[str]:
        """获取支持的编码器列表"""
        return list(self.encoders.keys())
    
    def get_encoder_info(self, encoder: str) -> Dict[str, Any]:
        """获取编码器信息"""
        if encoder not in self.encoders:
            raise ValueError(f"不支持的编码器: {encoder}")
        return self.encoders[encoder].copy()
    
    def get_quality_presets(self) -> Dict[str, Dict[str, Any]]:
        """获取质量预设"""
        return self.quality_presets.copy()
    
    def build_encoding_params(self, encoder: str, quality: str = "medium", 
                            custom_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        构建编码参数
        
        Args:
            encoder: 编码器名称
            quality: 质量预设
            custom_params: 自定义参数
            
        Returns:
            编码参数字典
        """
        if encoder not in self.encoders:
            raise ValueError(f"不支持的编码器: {encoder}")
        
        if quality not in self.quality_presets:
            raise ValueError(f"不支持的质量预设: {quality}")
        
        encoder_info = self.encoders[encoder]
        quality_info = self.quality_presets[quality]
        
        # 基础参数
        params = {
            "codec": encoder_info["codec"],
            "preset": quality_info["preset"],
            "crf": max(0, min(encoder_info["crf_range"][1], 
                             encoder_info["default_crf"] + quality_info["crf_offset"]))
        }
        
        # 交错隐藏特定参数
        params.update({
            "keyint_min": 1,
            "g": 3,  # GOP大小设为3（匹配交错模式）
            "bf": 2,  # B帧数量
            "b_strategy": 2,
            "flags": "+cgop"  # 关闭GOP
        })
        
        # 应用自定义参数
        if custom_params:
            params.update(custom_params)
        
        self.logger.info(f"构建编码参数: {encoder} + {quality} = {params}")
        return params
    
    def build_ffmpeg_args(self, params: Dict[str, Any]) -> List[str]:
        """
        将参数转换为FFmpeg命令行参数
        
        Args:
            params: 编码参数字典
            
        Returns:
            FFmpeg参数列表
        """
        args = []
        
        # 视频编码器
        args.extend(["-c:v", params["codec"]])
        
        # 预设
        if "preset" in params:
            args.extend(["-preset", str(params["preset"])])
        
        # CRF质量
        if "crf" in params:
            args.extend(["-crf", str(params["crf"])])
        
        # GOP设置
        if "g" in params:
            args.extend(["-g", str(params["g"])])
        
        if "keyint_min" in params:
            args.extend(["-keyint_min", str(params["keyint_min"])])
        
        # B帧设置
        if "bf" in params:
            args.extend(["-bf", str(params["bf"])])
        
        if "b_strategy" in params:
            args.extend(["-b_strategy", str(params["b_strategy"])])
        
        # 其他标志
        if "flags" in params:
            args.extend(["-flags", params["flags"]])
        
        # 像素格式
        args.extend(["-pix_fmt", "yuv420p"])
        
        return args
    
    def validate_encoder_availability(self, encoder: str) -> bool:
        """
        验证编码器是否可用
        
        Args:
            encoder: 编码器名称
            
        Returns:
            是否可用
        """
        try:
            import subprocess
            
            if encoder not in self.encoders:
                return False
            
            codec = self.encoders[encoder]["codec"]
            
            # 测试编码器是否可用
            cmd = ["ffmpeg", "-hide_banner", "-encoders"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            return codec in result.stdout
            
        except Exception as e:
            self.logger.warning(f"验证编码器可用性失败: {e}")
            return False
    
    def get_recommended_settings(self, use_case: str) -> Dict[str, Any]:
        """
        获取推荐设置
        
        Args:
            use_case: 使用场景
            
        Returns:
            推荐设置
        """
        recommendations = {
            "copyright_protection": {
                "encoder": "h264",
                "quality": "high",
                "description": "版权保护场景，需要高质量保留细节"
            },
            "content_verification": {
                "encoder": "h264",
                "quality": "medium",
                "description": "内容验证场景，平衡质量和大小"
            },
            "multi_version": {
                "encoder": "h265",
                "quality": "high",
                "description": "多版本管理，使用高效编码器"
            },
            "education": {
                "encoder": "h264",
                "quality": "medium",
                "description": "教育场景，兼容性优先"
            },
            "web_streaming": {
                "encoder": "h264",
                "quality": "web",
                "description": "网络流媒体优化"
            },
            "archive": {
                "encoder": "h265",
                "quality": "high",
                "description": "长期存档，高压缩比"
            }
        }
        
        return recommendations.get(use_case, recommendations["content_verification"])
