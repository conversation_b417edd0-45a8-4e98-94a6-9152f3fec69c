"""
视频交错隐藏技术核心类
实现主视频与辅助视频的交错合并，并在重新编码后隐藏辅助内容
"""

import os
import subprocess
import tempfile
import logging
import json
import time
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any
from .presets import PresetManager
from utils.gpu_utils import GPUAccelerator

class VideoInterleaver:
    """视频交错处理核心类"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化视频交错器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 加载配置
        self.config = self._load_config(config_path)
        
        # 设置工具路径
        self.ffmpeg_path = self._get_tool_path("ffmpeg")
        self.ffprobe_path = self._get_tool_path("ffprobe")
        
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp(prefix="video_interleave_")

        # 初始化预设管理器
        self.preset_manager = PresetManager()

        # 初始化GPU加速器
        self.gpu_accelerator = GPUAccelerator(self.ffmpeg_path)

        # 检查GPU加速配置 - 使用正确的参数实现
        self.gpu_enabled = self.config.get("encoding", {}).get("enable_gpu", True)
        self.fallback_to_cpu = self.config.get("encoding", {}).get("fallback_to_cpu", True)

        if self.gpu_enabled and self.gpu_accelerator.is_gpu_available():
            self.logger.info(f"GPU加速已启用: {self.gpu_accelerator.get_gpu_info_summary()}")
            self.logger.info("使用修复后的NVENC参数，参考video_interleave_hiding.py实现")
        else:
            self.logger.info("使用CPU编码")

        # 编码器配置映射
        self.codec_configs = {
            "h264": {
                "codec": "libx264",
                "preset": "slow",
                "crf": 18,
                "keyint_min": 1,
                "g": 3,  # GOP大小设为3（匹配交错模式）
                "bf": 2,  # B帧数量
                "b_strategy": 2
            },
            "h265": {
                "codec": "libx265",
                "preset": "slow",
                "crf": 20,
                "keyint_min": 1,
                "g": 3,
                "bf": 2,
                "b_strategy": 2
            }
        }
        
        # 验证工具路径
        self._validate_tools()

        self.logger.info("VideoInterleaver 初始化完成")

    def _get_best_encoder(self, gpu_acceleration="auto", quality="medium"):
        """获取最佳编码器配置 - 参考video_interleave_hiding.py"""
        if gpu_acceleration == "disabled":
            return "libx264", []

        # 自动检测GPU
        if gpu_acceleration == "auto":
            gpu_type = self._detect_gpu()

            if gpu_type == "nvidia":
                return self._get_nvidia_encoder(quality)
            elif gpu_type == "amd":
                return self._get_amd_encoder(quality)
            elif gpu_type == "intel":
                return self._get_intel_encoder(quality)

        # 手动指定
        elif gpu_acceleration == "nvidia":
            return self._get_nvidia_encoder(quality)
        elif gpu_acceleration == "amd":
            return self._get_amd_encoder(quality)
        elif gpu_acceleration == "intel":
            return self._get_intel_encoder(quality)

        # 默认CPU编码
        return "libx264", []

    def _detect_gpu(self):
        """检测系统GPU类型 - 参考video_interleave_hiding.py的简洁实现"""
        try:
            # NVIDIA检测
            result = subprocess.run(["nvidia-smi"], stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  text=True, timeout=5)
            if result.returncode == 0 and "NVIDIA-SMI" in result.stdout:
                return "nvidia"
        except (FileNotFoundError, subprocess.TimeoutExpired):
            pass

        try:
            # AMD检测
            result = subprocess.run(["rocm-smi"], stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  text=True, timeout=5)
            if result.returncode == 0:
                return "amd"
        except (FileNotFoundError, subprocess.TimeoutExpired):
            pass

        try:
            # Intel检测
            result = subprocess.run(["intel_gpu_top", "-v"], stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  text=True, timeout=5)
            if result.returncode == 0 or "Intel" in result.stderr:
                return "intel"
        except (FileNotFoundError, subprocess.TimeoutExpired):
            pass

        return None

    def _get_nvidia_encoder(self, quality="medium"):
        """获取NVIDIA编码器配置 - 修复NVENC参数错误"""
        encoder = "h264_nvenc"

        # 修复NVENC参数：使用正确的VBR模式参数
        if quality == "high":
            params = [
                "-preset", "p7",
                "-rc", "vbr",
                "-cq", "18",
                "-b:v", "10M",  # 设置目标码率
                "-maxrate", "15M",  # 设置最大码率
                "-bufsize", "20M"   # 设置缓冲区大小
            ]
        else:
            params = [
                "-preset", "p4",
                "-rc", "vbr",
                "-cq", "23",
                "-b:v", "8M",   # 设置目标码率
                "-maxrate", "12M",  # 设置最大码率
                "-bufsize", "16M"   # 设置缓冲区大小
            ]

        return encoder, params

    def _get_amd_encoder(self, quality="medium"):
        """获取AMD编码器配置 - 修复AMF参数"""
        encoder = "h264_amf"

        if quality == "high":
            params = [
                "-quality", "quality",
                "-rc", "vbr_peak",
                "-b:v", "10M",
                "-maxrate", "15M",
                "-qp_i", "18",
                "-qp_p", "20"
            ]
        else:
            params = [
                "-quality", "speed",
                "-rc", "vbr_peak",
                "-b:v", "8M",
                "-maxrate", "12M",
                "-qp_i", "23",
                "-qp_p", "25"
            ]
        return encoder, params

    def _get_intel_encoder(self, quality="medium"):
        """获取Intel编码器配置 - 修复QSV参数"""
        encoder = "h264_qsv"

        if quality == "high":
            params = [
                "-preset", "veryslow",
                "-global_quality", "18",
                "-b:v", "10M",
                "-maxrate", "15M"
            ]
        else:
            params = [
                "-preset", "faster",
                "-global_quality", "23",
                "-b:v", "8M",
                "-maxrate", "12M"
            ]
        return encoder, params
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path is None:
            # 使用默认配置路径
            current_dir = Path(__file__).parent.parent
            config_path = current_dir / "config.json"
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.logger.info(f"配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            self.logger.warning(f"配置文件加载失败: {e}，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "paths": {
                "ffmpeg": "ffmpeg",
                "ffprobe": "ffprobe"
            },
            "encoding": {
                "default_codec": "h264",
                "default_preset": "copyright_protection"
            }
        }
    
    def _get_tool_path(self, tool_name: str) -> str:
        """获取工具路径"""
        tool_path = self.config.get("paths", {}).get(tool_name, tool_name)
        
        # 如果是相对路径，转换为绝对路径
        if not os.path.isabs(tool_path):
            current_dir = Path(__file__).parent.parent
            tool_path = current_dir / tool_path
        
        return str(tool_path)

    def _validate_tools(self):
        """验证FFmpeg和FFprobe工具是否可用"""
        tools = {
            "FFmpeg": self.ffmpeg_path,
            "FFprobe": self.ffprobe_path
        }

        for tool_name, tool_path in tools.items():
            try:
                # 测试工具是否可执行
                result = subprocess.run([tool_path, "-version"],
                                      capture_output=True, text=True,
                                      encoding='utf-8', errors='ignore', timeout=10)
                if result.returncode == 0:
                    self.logger.info(f"{tool_name} 可用: {tool_path}")
                else:
                    self.logger.warning(f"{tool_name} 执行失败: {tool_path}")
            except FileNotFoundError:
                self.logger.error(f"{tool_name} 未找到: {tool_path}")
                raise FileNotFoundError(f"{tool_name} 工具未找到，请检查路径: {tool_path}")
            except subprocess.TimeoutExpired:
                self.logger.warning(f"{tool_name} 响应超时: {tool_path}")
            except Exception as e:
                self.logger.warning(f"{tool_name} 验证失败: {e}")
    
    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        获取视频信息

        Args:
            video_path: 视频文件路径

        Returns:
            包含视频信息的字典
        """
        try:
            cmd = [
                self.ffprobe_path,
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                video_path
            ]

            self.logger.info(f"执行FFprobe命令: {' '.join(cmd)}")
            # 在Windows上使用utf-8编码避免中文路径问题，添加30秒超时
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore', check=True, timeout=30)

            # 检查输出是否为空
            if not result.stdout or result.stdout.strip() == "":
                raise ValueError(f"FFprobe返回空结果，可能是视频文件损坏或格式不支持: {video_path}")

            self.logger.debug(f"FFprobe输出: {result.stdout[:200]}...")
            info = json.loads(result.stdout)

            # 提取视频流信息
            video_stream = None
            for stream in info.get("streams", []):
                if stream.get("codec_type") == "video":
                    video_stream = stream
                    break

            if not video_stream:
                raise ValueError("未找到视频流")

            # 安全地解析帧率
            fps_str = video_stream.get("r_frame_rate", "30/1")
            try:
                if "/" in fps_str:
                    num, den = fps_str.split("/")
                    fps = float(num) / float(den) if float(den) != 0 else 30.0
                else:
                    fps = float(fps_str)
            except (ValueError, ZeroDivisionError):
                fps = 30.0
                self.logger.warning(f"无法解析帧率 {fps_str}，使用默认值 30fps")

            return {
                "duration": float(info["format"]["duration"]),
                "width": int(video_stream["width"]),
                "height": int(video_stream["height"]),
                "fps": fps,
                "codec": video_stream["codec_name"],
                "bitrate": int(info["format"].get("bit_rate", 0))
            }

        except subprocess.CalledProcessError as e:
            self.logger.error(f"FFprobe执行失败: {e.stderr}")
            raise ValueError(f"无法获取视频信息，FFprobe错误: {e.stderr}")
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            self.logger.error(f"FFprobe输出: {result.stdout}")
            raise ValueError(f"FFprobe输出格式错误: {e}")
        except Exception as e:
            self.logger.error(f"获取视频信息失败: {e}")
            raise
    
    def normalize_videos(self, video_paths: List[str]) -> List[str]:
        """
        标准化视频参数（分辨率、帧率等）
        
        Args:
            video_paths: 视频文件路径列表
            
        Returns:
            标准化后的视频文件路径列表
        """
        if not video_paths:
            raise ValueError("视频路径列表不能为空")
        
        try:
            # 获取所有视频信息
            video_infos = []
            for i, path in enumerate(video_paths):
                self.logger.info(f"获取视频 {i+1} 信息: {path}")

                if not os.path.exists(path):
                    raise FileNotFoundError(f"视频文件不存在: {path}")

                info = self.get_video_info(path)
                video_infos.append(info)
                self.logger.info(f"视频信息 {path}: {info['width']}x{info['height']} @ {info['fps']:.2f}fps")

            # 确定目标参数（使用主视频的参数，但优化分辨率）
            target_info = video_infos[0]
            main_width = target_info["width"]
            main_height = target_info["height"]
            target_fps = target_info["fps"]

            # 优化分辨率：选择更高质量的分辨率
            target_width, target_height = self._optimize_resolution(main_width, main_height)

            self.logger.info(f"主视频分辨率: {main_width}x{main_height}")
            self.logger.info(f"目标分辨率: {target_width}x{target_height} @ {target_fps:.2f}fps")

            # 标准化所有视频
            normalized_paths = []
            for i, (path, info) in enumerate(zip(video_paths, video_infos)):
                try:
                    if (info["width"] == target_width and
                        info["height"] == target_height and
                        abs(info["fps"] - target_fps) < 0.1):
                        # 参数已匹配，直接使用原文件
                        normalized_paths.append(path)
                        self.logger.info(f"视频 {i+1} 参数已匹配，无需转换")
                    else:
                        # 需要转换
                        self.logger.info(f"视频 {i+1} 需要转换参数")
                        output_path = os.path.join(self.temp_dir, f"normalized_{i}.mp4")
                        self._convert_video_params(path, output_path, target_width, target_height, target_fps)
                        normalized_paths.append(output_path)
                        self.logger.info(f"视频 {i+1} 已标准化: {output_path}")

                except Exception as e:
                    self.logger.error(f"标准化视频 {i+1} 失败: {e}")
                    raise RuntimeError(f"标准化视频 {i+1} ({path}) 失败: {e}")

            self.logger.info(f"所有视频标准化完成，共处理 {len(normalized_paths)} 个视频")
            return normalized_paths

        except Exception as e:
            self.logger.error(f"视频标准化过程失败: {e}")
            raise

    def match_video_duration(self, main_video: str, aux_video: str) -> str:
        """
        匹配辅助视频时长到主视频时长

        Args:
            main_video: 主视频路径
            aux_video: 辅助视频路径

        Returns:
            处理后的辅助视频路径
        """
        try:
            # 获取视频时长
            main_info = self.get_video_info(main_video)
            aux_info = self.get_video_info(aux_video)

            main_duration = main_info["duration"]
            aux_duration = aux_info["duration"]

            self.logger.info(f"主视频时长: {main_duration:.2f}秒")
            self.logger.info(f"辅助视频时长: {aux_duration:.2f}秒")

            # 如果时长相近（差异小于1秒），直接返回
            if abs(main_duration - aux_duration) < 1.0:
                self.logger.info("视频时长已匹配，无需处理")
                return aux_video

            # 生成处理后的辅助视频路径
            processed_aux = os.path.join(self.temp_dir, "processed_aux.mp4")

            if aux_duration < main_duration:
                # 辅助视频较短，需要循环
                self.logger.info("辅助视频较短，进行循环处理...")
                return self._loop_video_to_duration(aux_video, processed_aux, main_duration)
            else:
                # 辅助视频较长，需要截取
                self.logger.info("辅助视频较长，进行截取处理...")
                return self._trim_video_to_duration(aux_video, processed_aux, main_duration)

        except Exception as e:
            self.logger.error(f"视频时长匹配失败: {e}")
            return aux_video  # 返回原视频

    def _loop_video_to_duration(self, input_video: str, output_video: str, target_duration: float) -> str:
        """
        循环视频到指定时长

        Args:
            input_video: 输入视频路径
            output_video: 输出视频路径
            target_duration: 目标时长（秒）

        Returns:
            输出视频路径
        """
        try:
            # 优先使用GPU加速
            if self.gpu_enabled:
                cmd = [
                    self.ffmpeg_path,
                    "-stream_loop", "-1",  # 无限循环
                    "-i", input_video,
                    "-t", str(target_duration),  # 指定输出时长
                    "-c:v", "h264_nvenc",
                    "-preset", "fast",
                    "-b:v", "8000k",
                    "-c:a", "aac",
                    "-y", output_video
                ]
            else:
                cmd = [
                    self.ffmpeg_path,
                    "-stream_loop", "-1",  # 无限循环
                    "-i", input_video,
                    "-t", str(target_duration),  # 指定输出时长
                    "-c:v", "libx264",
                    "-preset", "fast",
                    "-crf", "23",
                    "-c:a", "aac",
                    "-y", output_video
                ]

            self.logger.info(f"视频循环处理使用: {'GPU加速' if self.gpu_enabled else 'CPU编码'}")
            # 添加超时保护，避免长时间卡死
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore', check=True, timeout=300)

            self.logger.info(f"视频循环处理完成: {output_video}")
            return output_video

        except subprocess.CalledProcessError as e:
            self.logger.error(f"视频循环处理失败: {e.stderr}")
            raise

    def _trim_video_to_duration(self, input_video: str, output_video: str, target_duration: float) -> str:
        """
        截取视频到指定时长

        Args:
            input_video: 输入视频路径
            output_video: 输出视频路径
            target_duration: 目标时长（秒）

        Returns:
            输出视频路径
        """
        try:
            cmd = [
                self.ffmpeg_path,
                "-i", input_video,
                "-t", str(target_duration),  # 指定输出时长
                "-c:v", "libx264",
                "-c:a", "aac",
                "-y", output_video
            ]

            result = subprocess.run(cmd, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore', check=True)

            self.logger.info(f"视频截取处理完成: {output_video}")
            return output_video

        except subprocess.CalledProcessError as e:
            self.logger.error(f"视频截取处理失败: {e.stderr}")
            raise
    
    def _convert_video_params(self, input_path: str, output_path: str,
                            width: int, height: int, fps: float):
        """
        转换视频参数

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            width: 目标宽度
            height: 目标高度
            fps: 目标帧率
        """
        # 构建基础命令，包含SAR标准化
        cmd = [
            self.ffmpeg_path,
            "-i", input_path,
            "-vf", f"scale={width}:{height},fps={fps},setsar=1"
        ]

        # 选择编码器（参考video_interleave_hiding.py的简洁实现）
        if self.gpu_enabled:
            # 获取最佳编码器配置
            encoder, gpu_params = self._get_best_encoder("auto", "medium")

            if encoder != "libx264":  # 如果有GPU编码器可用
                self.logger.info(f"尝试使用GPU加速: {encoder}")

                gpu_cmd = cmd.copy()
                gpu_cmd.extend(["-c:v", encoder])
                gpu_cmd.extend(gpu_params)  # 添加GPU特定参数
                gpu_cmd.extend(["-c:a", "copy", "-y", output_path])

                try:
                    self.logger.info(f"执行GPU命令: {' '.join(gpu_cmd)}")
                    result = subprocess.run(gpu_cmd, capture_output=True, text=True,
                                          encoding='utf-8', errors='ignore', check=True, timeout=300)

                    if os.path.exists(output_path):
                        file_size = os.path.getsize(output_path)
                        self.logger.info(f"GPU视频转换成功: {output_path} (大小: {file_size} 字节)")
                        return  # GPU成功，直接返回

                except Exception as e:
                    self.logger.warning(f"GPU编码失败，回退到CPU编码: {e}")
                    # 删除可能的不完整文件
                    if os.path.exists(output_path):
                        os.remove(output_path)

        # 使用CPU编码（回退方案）
        self.logger.info("使用CPU编码进行视频参数转换")
        cpu_cmd = cmd.copy()
        cpu_cmd.extend([
            "-c:v", "libx264",
            "-crf", "18",
            "-preset", "fast",
            "-c:a", "copy",
            "-y", output_path
        ])

        # 执行CPU编码
        try:
            # 简化日志输出，避免长文件名导致问题
            self.logger.info(f"开始CPU编码转换...")
            result = subprocess.run(cpu_cmd, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore', check=True, timeout=300)

            # 检查输出文件是否生成
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                self.logger.info(f"CPU视频转换成功: {output_path} (大小: {file_size} 字节)")
            else:
                raise RuntimeError("输出文件未生成")

        except subprocess.TimeoutExpired:
            self.logger.error(f"视频参数转换超时")
            raise RuntimeError("视频转换超时")
        except subprocess.CalledProcessError as e:
            self.logger.error(f"视频参数转换失败，返回码: {e.returncode}")
            # 简化错误信息，避免长文本导致问题
            error_msg = str(e.stderr)[:200] if e.stderr else "未知错误"
            self.logger.error(f"错误信息: {error_msg}")
            raise RuntimeError(f"FFmpeg执行失败")
        except Exception as e:
            self.logger.error(f"视频参数转换异常: {str(e)[:100]}")
            raise RuntimeError("视频转换异常")
    
    def interleave_videos(self, main_video: str, aux_videos: List[str],
                         output_path: str, codec: str = "h264",
                         preset: str = "copyright_protection") -> bool:
        """
        交错合并视频

        Args:
            main_video: 主视频路径
            aux_videos: 辅助视频路径列表
            output_path: 输出文件路径
            codec: 编码器类型
            preset: 预设模板名称

        Returns:
            是否成功
        """
        try:
            # 验证输入文件
            if not os.path.exists(main_video):
                raise FileNotFoundError(f"主视频文件不存在: {main_video}")

            if len(aux_videos) != 1:
                raise ValueError("当前版本只支持一个辅助视频")

            aux_video = aux_videos[0]
            if not os.path.exists(aux_video):
                raise FileNotFoundError(f"辅助视频文件不存在: {aux_video}")

            self.logger.info(f"开始交错处理: 主视频 + 辅助视频")
            self.logger.info(f"使用预设: {preset}")
            self.logger.info(f"主视频: {main_video}")
            self.logger.info(f"辅助视频: {aux_video}")

            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 匹配辅助视频时长到主视频时长
            self.logger.info("开始匹配视频时长...")
            processed_aux_video = self.match_video_duration(main_video, aux_video)

            # 准备处理后的视频列表
            all_videos = [main_video, processed_aux_video]

            # 标准化视频参数
            self.logger.info("开始标准化视频参数...")
            normalized_videos = self.normalize_videos(all_videos)

            # 构建FFmpeg交错命令
            self.logger.info("构建FFmpeg命令...")
            cmd = self._build_interleave_command(normalized_videos, output_path, codec, preset)

            # 执行命令（使用进度监控）
            self.logger.info(f"执行FFmpeg命令: {' '.join(cmd)}")
            success = self._run_ffmpeg_with_progress(cmd, "视频交错处理")

            # 检查输出文件是否生成
            if success and os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                self.logger.info(f"视频交错完成: {output_path} (大小: {file_size} 字节)")
                return True
            else:
                self.logger.error("输出文件未生成")
                return False

        except subprocess.CalledProcessError as e:
            self.logger.error(f"FFmpeg执行失败: {e.stderr}")
            return False
        except Exception as e:
            self.logger.error(f"视频交错失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return False
    
    def _build_interleave_command(self, video_paths: List[str], output_path: str,
                                codec: str, preset: str) -> List[str]:
        """
        构建FFmpeg交错命令

        Args:
            video_paths: 标准化后的视频路径列表
            output_path: 输出路径
            codec: 编码器
            preset: 预设模板

        Returns:
            FFmpeg命令列表
        """
        cmd = [self.ffmpeg_path]

        # 添加输入文件
        for path in video_paths:
            cmd.extend(["-i", path])

        # 获取预设信息
        try:
            preset_info = self.preset_manager.get_preset_info(preset)
            interleave_pattern = preset_info.get("interleave_pattern", "AB")
            encoding_params = preset_info.get("encoding_params", {})
        except Exception as e:
            self.logger.warning(f"获取预设信息失败，使用默认配置: {e}")
            interleave_pattern = "AB"
            encoding_params = {}

        # 构建自定义交错滤镜
        filter_complex = self._build_custom_interleave_filter(video_paths, interleave_pattern, encoding_params)
        cmd.extend(["-filter_complex", filter_complex])

        # 映射输出
        cmd.extend(["-map", "[out]"])

        # 添加编码参数
        try:
            # 检查是否启用GPU加速
            enable_gpu = encoding_params.get("enable_gpu", False)
            bitrate = encoding_params.get("bitrate", "50000k")

            if enable_gpu and self.gpu_enabled:
                # 使用优化的编码器选择逻辑
                encoder, gpu_params = self._get_best_encoder("auto", "medium")

                if encoder != "libx264":  # 如果有GPU编码器可用
                    self.logger.info(f"使用GPU加速编码: {encoder}")

                    cmd.extend(["-c:v", encoder])
                    cmd.extend(gpu_params)  # 添加GPU特定参数
                    cmd.extend([
                        "-b:v", bitrate,
                        "-g", str(encoding_params.get("gop_size", 3))
                    ])
                else:
                    # GPU不可用，使用CPU编码
                    self.logger.info("GPU不可用，使用CPU编码")
                    self._add_cpu_encoding_params(cmd, encoding_params)
            else:
                # 使用CPU编码
                self.logger.info("使用CPU编码")
                self._add_cpu_encoding_params(cmd, encoding_params)

            # 设置帧率
            cmd.extend(["-r", str(encoding_params.get("fps", 120))])

        except Exception as e:
            self.logger.warning(f"使用预设配置失败，使用默认配置: {e}")
            # 回退到默认配置
            codec_config = self.codec_configs.get(codec, self.codec_configs["h264"])
            cmd.extend(["-c:v", codec_config["codec"]])
            cmd.extend(["-preset", codec_config["preset"]])
            cmd.extend(["-crf", str(codec_config["crf"])])
            cmd.extend(["-keyint_min", str(codec_config["keyint_min"])])
            cmd.extend(["-g", str(codec_config["g"])])
            cmd.extend(["-bf", str(codec_config["bf"])])
            cmd.extend(["-b_strategy", str(codec_config["b_strategy"])])
            cmd.extend(["-r", "120"])
            cmd.extend(["-b:v", "50000k"])

        # 输出文件
        cmd.extend(["-y", output_path])

        return cmd

    def _build_custom_interleave_filter(self, video_paths: List[str], pattern: str, encoding_params: Dict[str, Any]) -> str:
        """
        构建自定义交错滤镜

        Args:
            video_paths: 视频路径列表
            pattern: 交错模式 ("AB", "ABB", "AABB")
            encoding_params: 编码参数

        Returns:
            FFmpeg filter_complex字符串
        """
        if len(video_paths) != 2:
            raise ValueError("当前只支持两个视频的交错")

        fps = encoding_params.get("fps", 120)

        # 为输入视频设置标签、帧率和SAR标准化
        filter_parts = []
        filter_parts.append(f"[0:v]fps={fps},setsar=1[a]")
        filter_parts.append(f"[1:v]fps={fps},setsar=1[b]")

        # 真正的帧级交错：使用blend滤镜的mix模式实现逐帧切换
        if pattern == "AB":
            # 寒星AB模式: ABABABAB (1:1交错)
            # 使用blend滤镜的mix模式，根据帧号决定显示哪个视频
            interleave_filter = f"[a][b]blend=all_mode=mix:all_expr='if(mod(N,2),A,B)',fps={fps}[out]"
        elif pattern == "ABB":
            # 繁星AB模式: ABBABBABB (1:2交错)
            # 每3帧中：第1帧显示A，第2、3帧显示B
            interleave_filter = f"[a][b]blend=all_mode=mix:all_expr='if(mod(N,3),B,A)',fps={fps}[out]"
        elif pattern == "AABB":
            # 晨星AB模式: AABBAABB (2:2交错)
            # 每4帧中：前2帧显示A，后2帧显示B
            interleave_filter = f"[a][b]blend=all_mode=mix:all_expr='if(lt(mod(N,4),2),A,B)',fps={fps}[out]"
        else:
            # 默认使用AB模式
            self.logger.warning(f"未知的交错模式: {pattern}，使用默认AB模式")
            interleave_filter = f"[a][b]blend=all_mode=mix:all_expr='if(mod(N,2),A,B)',fps={fps}[out]"

        self.logger.info(f"使用交错模式: {pattern}, 滤镜: {interleave_filter}")

        filter_parts.append(interleave_filter)

        return ";".join(filter_parts)

    def _optimize_resolution(self, width: int, height: int) -> tuple:
        """
        优化分辨率，倾向于选择1080p或1920p

        Args:
            width: 原始宽度
            height: 原始高度

        Returns:
            (优化后的宽度, 优化后的高度)
        """
        # 计算宽高比
        aspect_ratio = width / height

        # 判断是横屏还是竖屏
        if width > height:
            # 横屏视频
            if width < 1920:
                # 升级到1920p
                target_width = 1920
                target_height = int(1920 / aspect_ratio)
                # 确保高度是偶数
                target_height = target_height - (target_height % 2)
            else:
                # 保持原分辨率
                target_width = width
                target_height = height
        else:
            # 竖屏视频
            if height < 1920:
                # 升级到1920p高度
                target_height = 1920
                target_width = int(1920 * aspect_ratio)
                # 确保宽度是偶数
                target_width = target_width - (target_width % 2)
            else:
                # 保持原分辨率
                target_width = width
                target_height = height

        self.logger.info(f"分辨率优化: {width}x{height} -> {target_width}x{target_height}")
        return target_width, target_height

    def _run_ffmpeg_with_progress(self, cmd: List[str], task_name: str) -> bool:
        """
        运行FFmpeg命令并显示进度

        Args:
            cmd: FFmpeg命令列表
            task_name: 任务名称

        Returns:
            是否成功
        """
        try:
            self.logger.info(f"开始{task_name}...")

            # 启动进程（添加超时保护）
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )

            # 监控进度（添加超时保护）
            last_progress = 0
            stderr_lines = []
            start_time = time.time()
            max_duration = 600  # 10分钟超时

            while True:
                try:
                    stderr_line = process.stderr.readline()
                    if stderr_line == '' and process.poll() is not None:
                        break

                    # 检查超时
                    current_time = time.time()
                    if current_time - start_time > max_duration:
                        self.logger.warning(f"{task_name}超时，终止进程")
                        process.terminate()
                        process.wait(timeout=5)
                        return False

                    if stderr_line:
                        stderr_lines.append(stderr_line)

                        # 解析进度信息
                        if 'frame=' in stderr_line and 'fps=' in stderr_line:
                            try:
                                if current_time - last_progress > 5:  # 每5秒更新一次
                                    self.logger.info(f"{task_name}进行中... ({int(current_time - start_time)}秒)")
                                    last_progress = current_time
                            except:
                                pass

                except Exception as e:
                    self.logger.warning(f"{task_name}监控异常: {e}")
                    break

            # 等待进程完成
            try:
                return_code = process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.logger.error(f"{task_name}进程无法正常结束，强制终止")
                process.kill()
                return False

            if return_code == 0:
                self.logger.info(f"{task_name}完成")
                return True
            else:
                # 输出错误信息
                stderr_output = ''.join(stderr_lines)
                self.logger.error(f"{task_name}失败: {stderr_output}")
                return False

        except Exception as e:
            self.logger.error(f"{task_name}异常: {e}")
            return False

    def _add_cpu_encoding_params(self, cmd: List[str], encoding_params: Dict[str, Any]):
        """
        添加CPU编码参数

        Args:
            cmd: FFmpeg命令列表
            encoding_params: 编码参数
        """
        cmd.extend(["-c:v", encoding_params.get("codec", "libx264")])
        cmd.extend(["-preset", encoding_params.get("preset", "slow")])
        cmd.extend(["-crf", str(encoding_params.get("crf", 18))])
        cmd.extend(["-keyint_min", str(encoding_params.get("keyint_min", 1))])
        cmd.extend(["-g", str(encoding_params.get("gop_size", 3))])
        cmd.extend(["-bf", str(encoding_params.get("b_frames", 2))])
        cmd.extend(["-b_strategy", str(encoding_params.get("b_strategy", 2))])
        cmd.extend(["-b:v", encoding_params.get("bitrate", "50000k")])
    
    def test_compression_effect(self, interleaved_video: str, test_output: str, 
                              compression_params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        测试压缩后的显示效果
        
        Args:
            interleaved_video: 交错后的视频路径
            test_output: 测试输出路径
            compression_params: 压缩参数
            
        Returns:
            测试结果字典
        """
        if compression_params is None:
            compression_params = {
                "codec": "libx264",
                "crf": 23,
                "preset": "medium"
            }
        
        try:
            # 构建压缩命令
            cmd = [
                self.ffmpeg_path,
                "-i", interleaved_video,
                "-c:v", compression_params["codec"],
                "-crf", str(compression_params["crf"]),
                "-preset", compression_params["preset"],
                "-y", test_output
            ]
            
            # 执行压缩
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore', check=True)
            
            # 获取原始和压缩后的视频信息
            original_info = self.get_video_info(interleaved_video)
            compressed_info = self.get_video_info(test_output)
            
            # 计算压缩效果
            original_size = os.path.getsize(interleaved_video)
            compressed_size = os.path.getsize(test_output)
            compression_ratio = compressed_size / original_size
            
            result_info = {
                "success": True,
                "original_size": original_size,
                "compressed_size": compressed_size,
                "compression_ratio": compression_ratio,
                "original_duration": original_info["duration"],
                "compressed_duration": compressed_info["duration"],
                "frame_loss_ratio": 1 - (compressed_info["duration"] / original_info["duration"])
            }
            
            self.logger.info(f"压缩测试完成: 压缩比 {compression_ratio:.2f}, 帧丢失率 {result_info['frame_loss_ratio']:.2f}")
            return result_info
            
        except Exception as e:
            self.logger.error(f"压缩测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    def cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                self.logger.info(f"临时目录已清理: {self.temp_dir}")
        except Exception as e:
            self.logger.warning(f"清理临时文件失败: {e}")
    
    def __del__(self):
        """析构函数，自动清理"""
        self.cleanup()
