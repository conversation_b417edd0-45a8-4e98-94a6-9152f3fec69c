"""
预设模板管理模块
"""

import json
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

class PresetManager:
    """预设模板管理类"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 内置预设模板 - 参考video_interleave_hiding.py的简洁设计
        self.builtin_presets = {
            "hanxing_ab": {
                "name": "寒星AB模式",
                "description": "1:1交错 (ABABABAB)，适用于日常使用",
                "pattern": "AB",
                "gop_size": 2,
                "bframes": 1,
                "encoding_params": {
                    "codec": "libx264",
                    "crf": 18,
                    "preset": "fast",
                    "gop_size": 2,
                    "b_frames": 1,
                    "keyint_min": 1,
                    "fps": 120,
                    "bitrate": "50000k",
                    "enable_gpu": True
                }
            },
            "fanxing_ab": {
                "name": "繁星AB模式",
                "description": "1:2交错 (ABBABBABB)，专业用途，增强隐藏效果",
                "pattern": "ABB",
                "gop_size": 3,
                "bframes": 2,
                "encoding_params": {
                    "codec": "libx264",
                    "crf": 18,
                    "preset": "slow",
                    "gop_size": 3,
                    "b_frames": 2,
                    "keyint_min": 1,
                    "fps": 120,
                    "bitrate": "50000k",
                    "enable_gpu": True
                }
            },

            "chenxing_ab": {
                "name": "晨星AB模式",
                "description": "2:2交错 (AABBAABB)，存储优化，平衡模式",
                "pattern": "AABB",
                "gop_size": 4,
                "bframes": 2,
                "encoding_params": {
                    "codec": "libx264",
                    "crf": 20,
                    "preset": "medium",
                    "gop_size": 4,
                    "b_frames": 2,
                    "keyint_min": 1,
                    "fps": 120,
                    "bitrate": "40000k",
                    "enable_gpu": True
                }
            }
        }
        
        # 加载用户自定义预设
        self.custom_presets = {}
        if config_path:
            self.load_custom_presets(config_path)
    
    def get_preset_list(self) -> List[str]:
        """获取所有预设名称列表"""
        presets = list(self.builtin_presets.keys()) + list(self.custom_presets.keys())
        return sorted(presets)
    
    def get_preset_info(self, preset_name: str) -> Dict[str, Any]:
        """
        获取预设信息
        
        Args:
            preset_name: 预设名称
            
        Returns:
            预设信息字典
        """
        if preset_name in self.builtin_presets:
            return self.builtin_presets[preset_name].copy()
        elif preset_name in self.custom_presets:
            return self.custom_presets[preset_name].copy()
        else:
            raise ValueError(f"未找到预设: {preset_name}")
    
    def get_presets_by_category(self, category: str) -> List[str]:
        """
        按类别获取预设列表
        
        Args:
            category: 类别名称
            
        Returns:
            预设名称列表
        """
        presets = []
        
        # 检查内置预设
        for name, info in self.builtin_presets.items():
            if info.get("category") == category:
                presets.append(name)
        
        # 检查自定义预设
        for name, info in self.custom_presets.items():
            if info.get("category") == category:
                presets.append(name)
        
        return sorted(presets)
    
    def get_categories(self) -> List[str]:
        """获取所有类别列表"""
        categories = set()
        
        # 收集内置预设的类别
        for info in self.builtin_presets.values():
            if "category" in info:
                categories.add(info["category"])
        
        # 收集自定义预设的类别
        for info in self.custom_presets.values():
            if "category" in info:
                categories.add(info["category"])
        
        return sorted(list(categories))
    
    def create_custom_preset(self, name: str, preset_data: Dict[str, Any]) -> bool:
        """
        创建自定义预设
        
        Args:
            name: 预设名称
            preset_data: 预设数据
            
        Returns:
            是否成功
        """
        try:
            # 验证必要字段
            required_fields = ["name", "description", "encoding_params"]
            for field in required_fields:
                if field not in preset_data:
                    raise ValueError(f"缺少必要字段: {field}")
            
            # 添加默认值
            preset_data.setdefault("category", "custom")
            preset_data.setdefault("encoder", "h264")
            preset_data.setdefault("quality", "medium")
            
            self.custom_presets[name] = preset_data
            self.logger.info(f"创建自定义预设: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建自定义预设失败: {e}")
            return False
    
    def delete_custom_preset(self, name: str) -> bool:
        """
        删除自定义预设
        
        Args:
            name: 预设名称
            
        Returns:
            是否成功
        """
        if name in self.custom_presets:
            del self.custom_presets[name]
            self.logger.info(f"删除自定义预设: {name}")
            return True
        else:
            self.logger.warning(f"未找到自定义预设: {name}")
            return False
    
    def load_custom_presets(self, config_path: str) -> bool:
        """
        从文件加载自定义预设
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            是否成功
        """
        try:
            config_file = Path(config_path)
            if not config_file.exists():
                self.logger.info("自定义预设文件不存在，使用默认配置")
                return True
            
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if "custom_presets" in data:
                self.custom_presets = data["custom_presets"]
                self.logger.info(f"加载了 {len(self.custom_presets)} 个自定义预设")
            
            return True
            
        except Exception as e:
            self.logger.error(f"加载自定义预设失败: {e}")
            return False
    
    def save_custom_presets(self, config_path: str) -> bool:
        """
        保存自定义预设到文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            是否成功
        """
        try:
            config_file = Path(config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 读取现有配置
            existing_config = {}
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
            
            # 更新自定义预设
            existing_config["custom_presets"] = self.custom_presets
            
            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"保存了 {len(self.custom_presets)} 个自定义预设")
            return True
            
        except Exception as e:
            self.logger.error(f"保存自定义预设失败: {e}")
            return False
    
    def get_recommended_preset(self, use_case: str) -> str:
        """
        根据使用场景推荐预设
        
        Args:
            use_case: 使用场景
            
        Returns:
            推荐的预设名称
        """
        recommendations = {
            "high_quality": "fanxing_ab",
            "standard": "hanxing_ab",
            "efficient": "chenxing_ab",
            "default": "hanxing_ab"
        }
        
        return recommendations.get(use_case, recommendations["default"])
    
    def validate_preset(self, preset_data: Dict[str, Any]) -> List[str]:
        """
        验证预设数据
        
        Args:
            preset_data: 预设数据
            
        Returns:
            错误信息列表
        """
        errors = []
        
        # 检查必要字段
        required_fields = ["name", "description", "encoding_params"]
        for field in required_fields:
            if field not in preset_data:
                errors.append(f"缺少必要字段: {field}")
        
        # 检查编码参数
        if "encoding_params" in preset_data:
            encoding_params = preset_data["encoding_params"]
            
            if "codec" not in encoding_params:
                errors.append("编码参数中缺少codec字段")
            
            if "crf" in encoding_params:
                crf = encoding_params["crf"]
                if not isinstance(crf, int) or crf < 0 or crf > 51:
                    errors.append("CRF值必须是0-51之间的整数")
        
        return errors
