#!/usr/bin/env python3
"""
视频交错隐藏技术基础使用示例
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.interleaver import VideoInterleaver
from core.presets import PresetManager
from utils.logger import setup_logger

def basic_interleave_example():
    """基础交错示例"""
    print("🎬 基础视频交错示例")
    print("=" * 40)
    
    # 设置日志
    logger = setup_logger("example")
    
    try:
        # 创建视频交错器
        interleaver = VideoInterleaver()
        logger.info("视频交错器初始化成功")
        
        # 示例文件路径（请替换为实际文件路径）
        main_video = "path/to/main_video.mp4"
        aux_videos = [
            "path/to/aux_video1.mp4",
            "path/to/aux_video2.mp4"
        ]
        output_path = "output/interleaved_video.mp4"
        
        print(f"主视频: {main_video}")
        print(f"辅助视频: {aux_videos}")
        print(f"输出路径: {output_path}")
        
        # 检查文件是否存在
        if not os.path.exists(main_video):
            print("⚠️ 主视频文件不存在，这只是一个示例")
            print("请将文件路径替换为实际的视频文件路径")
            return
        
        # 执行交错处理
        print("\n开始交错处理...")
        success = interleaver.interleave_videos(
            main_video=main_video,
            aux_videos=aux_videos,
            output_path=output_path,
            preset="copyright_protection"
        )
        
        if success:
            print("✅ 交错处理完成！")
            
            # 测试压缩效果
            print("\n测试压缩效果...")
            test_output = "output/compressed_test.mp4"
            
            result = interleaver.test_compression_effect(
                interleaved_video=output_path,
                test_output=test_output
            )
            
            if result["success"]:
                print(f"压缩比: {result['compression_ratio']:.3f}")
                print(f"帧丢失率: {result['frame_loss_ratio']:.3f}")
                print(f"原始大小: {result['original_size'] / (1024*1024):.2f} MB")
                print(f"压缩后大小: {result['compressed_size'] / (1024*1024):.2f} MB")
            else:
                print(f"❌ 压缩测试失败: {result.get('error', '未知错误')}")
        else:
            print("❌ 交错处理失败")
        
        # 清理临时文件
        interleaver.cleanup()
        
    except Exception as e:
        logger.error(f"示例执行失败: {e}")
        print(f"❌ 示例执行失败: {e}")

def preset_usage_example():
    """预设使用示例"""
    print("\n🎛️ 预设模板使用示例")
    print("=" * 40)
    
    try:
        # 创建预设管理器
        preset_manager = PresetManager()
        
        # 获取所有预设
        presets = preset_manager.get_preset_list()
        print(f"可用预设 ({len(presets)} 个):")
        
        for preset_name in presets:
            preset_info = preset_manager.get_preset_info(preset_name)
            print(f"\n📋 {preset_info['name']}")
            print(f"   描述: {preset_info['description']}")
            print(f"   类别: {preset_info.get('category', '未分类')}")
            print(f"   编码器: {preset_info.get('encoder', 'h264')}")
            print(f"   推荐用于: {', '.join(preset_info.get('recommended_for', []))}")
        
        # 按类别显示预设
        print(f"\n📂 按类别分组:")
        categories = preset_manager.get_categories()
        for category in categories:
            category_presets = preset_manager.get_presets_by_category(category)
            print(f"  {category}: {', '.join(category_presets)}")
        
        # 获取推荐预设
        use_cases = ["copyright", "verification", "education", "web"]
        print(f"\n💡 使用场景推荐:")
        for use_case in use_cases:
            recommended = preset_manager.get_recommended_preset(use_case)
            preset_info = preset_manager.get_preset_info(recommended)
            print(f"  {use_case}: {preset_info['name']}")
        
    except Exception as e:
        print(f"❌ 预设示例失败: {e}")

def create_custom_preset_example():
    """创建自定义预设示例"""
    print("\n🛠️ 自定义预设创建示例")
    print("=" * 40)
    
    try:
        preset_manager = PresetManager()
        
        # 定义自定义预设
        custom_preset = {
            "name": "我的自定义预设",
            "description": "针对特定需求优化的自定义预设",
            "category": "custom",
            "encoder": "h264",
            "quality": "high",
            "use_case": "特殊应用场景",
            "recommended_for": ["特定用途", "实验性功能"],
            "encoding_params": {
                "codec": "h264",
                "crf": 16,
                "preset": "slow",
                "gop_size": 3,
                "b_frames": 2,
                "keyint_min": 1,
                "b_strategy": 2
            },
            "compression_test": {
                "target_crf": 20,
                "expected_hiding_rate": 0.92
            }
        }
        
        # 验证预设数据
        errors = preset_manager.validate_preset(custom_preset)
        if errors:
            print("❌ 预设验证失败:")
            for error in errors:
                print(f"   - {error}")
            return
        
        # 创建自定义预设
        success = preset_manager.create_custom_preset("my_custom", custom_preset)
        
        if success:
            print("✅ 自定义预设创建成功")
            
            # 显示创建的预设信息
            created_preset = preset_manager.get_preset_info("my_custom")
            print(f"预设名称: {created_preset['name']}")
            print(f"预设描述: {created_preset['description']}")
            
            # 保存到配置文件（可选）
            # preset_manager.save_custom_presets("custom_presets.json")
            
        else:
            print("❌ 自定义预设创建失败")
        
    except Exception as e:
        print(f"❌ 自定义预设示例失败: {e}")

def video_info_example():
    """视频信息获取示例"""
    print("\n📹 视频信息获取示例")
    print("=" * 40)
    
    try:
        interleaver = VideoInterleaver()
        
        # 示例视频文件（请替换为实际文件）
        video_file = "path/to/sample_video.mp4"
        
        if not os.path.exists(video_file):
            print("⚠️ 示例视频文件不存在")
            print("请将路径替换为实际的视频文件路径")
            return
        
        # 获取视频信息
        print(f"分析视频文件: {video_file}")
        video_info = interleaver.get_video_info(video_file)
        
        print(f"✅ 视频信息:")
        print(f"   时长: {video_info['duration']:.2f} 秒")
        print(f"   分辨率: {video_info['width']} x {video_info['height']}")
        print(f"   帧率: {video_info['fps']:.2f} fps")
        print(f"   编码器: {video_info['codec']}")
        print(f"   码率: {video_info['bitrate']} bps")
        
        # 清理
        interleaver.cleanup()
        
    except Exception as e:
        print(f"❌ 视频信息获取失败: {e}")

def main():
    """主函数"""
    print("🚀 视频交错隐藏技术 - 使用示例")
    print("=" * 60)
    
    # 运行各种示例
    examples = [
        ("基础交错功能", basic_interleave_example),
        ("预设模板使用", preset_usage_example),
        ("自定义预设创建", create_custom_preset_example),
        ("视频信息获取", video_info_example)
    ]
    
    for example_name, example_func in examples:
        try:
            example_func()
        except KeyboardInterrupt:
            print(f"\n⚠️ 用户中断了 {example_name} 示例")
            break
        except Exception as e:
            print(f"❌ {example_name} 示例执行异常: {e}")
    
    print("\n" + "=" * 60)
    print("📚 更多信息:")
    print("- 查看 README.md 了解详细使用说明")
    print("- 查看 docs/ 目录获取完整文档")
    print("- 运行 python main.py 启动图形界面")
    print("- 运行 python test_basic.py 进行基础测试")

if __name__ == "__main__":
    main()
