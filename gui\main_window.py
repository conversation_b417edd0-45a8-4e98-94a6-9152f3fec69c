"""
主窗口模块
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QTextEdit,
    QGroupBox, QProgressBar, QFileDialog, QMessageBox,
    QTabWidget, QListWidget, QSplitter, QFrame, QApplication
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon, QPalette, QColor

from core.interleaver import VideoInterleaver
from core.presets import PresetManager
from utils.logger import setup_logger, GuiLogHandler
# from utils.file_utils import FileUtils  # 暂时注释掉，避免导入错误

class ProcessingThread(QThread):
    """视频处理线程"""
    
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)
    
    def __init__(self, interleaver, main_video, aux_videos, output_path, preset):
        super().__init__()
        self.interleaver = interleaver
        self.main_video = main_video
        self.aux_videos = aux_videos
        self.output_path = output_path
        self.preset = preset
        
    def run(self):
        """运行处理任务"""
        try:
            self.log_message.emit("开始视频交错处理...")
            self.log_message.emit(f"主视频: {self.main_video}")
            self.log_message.emit(f"辅助视频: {self.aux_videos}")
            self.log_message.emit(f"输出路径: {self.output_path}")
            self.log_message.emit(f"使用预设: {self.preset}")
            self.progress_updated.emit(10)

            # 检查输入文件是否存在
            if not os.path.exists(self.main_video):
                raise FileNotFoundError(f"主视频文件不存在: {self.main_video}")

            for i, aux_video in enumerate(self.aux_videos):
                if not os.path.exists(aux_video):
                    raise FileNotFoundError(f"辅助视频文件不存在: {aux_video}")

            self.progress_updated.emit(20)

            success = self.interleaver.interleave_videos(
                main_video=self.main_video,
                aux_videos=self.aux_videos,
                output_path=self.output_path,
                preset=self.preset
            )

            self.progress_updated.emit(100)

            if success:
                self.log_message.emit("✅ 视频交错处理完成！")
                self.finished_signal.emit(True, self.output_path)
            else:
                self.log_message.emit("❌ 视频交错处理失败")
                self.finished_signal.emit(False, "处理失败")

        except Exception as e:
            import traceback
            error_msg = f"❌ 处理过程中发生错误: {e}"
            self.log_message.emit(error_msg)
            self.log_message.emit(f"错误详情: {traceback.format_exc()}")
            self.finished_signal.emit(False, str(e))

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger("MainWindow")
        
        # 初始化组件
        self.interleaver = None
        self.preset_manager = None
        self.processing_thread = None
        
        # 设置窗口
        self.setWindowTitle("视频交错隐藏工具 v1.0.0")
        self.setMinimumSize(1200, 800)

        # 设置合适的窗口大小，不要全屏
        screen = QApplication.primaryScreen().geometry()
        window_width = min(1400, int(screen.width() * 0.8))
        window_height = min(900, int(screen.height() * 0.8))
        self.resize(window_width, window_height)

        # 居中显示
        self.center_window()

        # 初始化UI
        self.init_ui()
        self.init_components()
        self.setup_logging()

    def center_window(self):
        """将窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
        self.logger.info("主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 垂直布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)

        # 文件选择组
        file_group = self.create_file_selection_group()
        main_layout.addWidget(file_group)

        # 预设模板组
        preset_group = self.create_preset_selection_group()
        main_layout.addWidget(preset_group)

        # 输出设置组
        output_group = self.create_output_settings_group()
        main_layout.addWidget(output_group)

        # 处理控制组
        control_group = self.create_processing_control_group()
        main_layout.addWidget(control_group)

        # 处理日志组
        log_panel = self.create_log_panel()
        main_layout.addWidget(log_panel)

        # 应用样式
        self.apply_dark_theme()
    

    
    def create_file_selection_group(self) -> QGroupBox:
        """创建文件选择组"""
        group = QGroupBox("文件选择")
        layout = QVBoxLayout(group)

        # 主视频选择
        main_layout = QHBoxLayout()
        main_layout.addWidget(QLabel("主视频:"))
        self.main_video_input = QLineEdit()
        self.main_video_input.setPlaceholderText("选择主视频文件...")
        main_layout.addWidget(self.main_video_input)

        self.main_video_btn = QPushButton("浏览")
        self.main_video_btn.clicked.connect(self.select_main_video)
        main_layout.addWidget(self.main_video_btn)
        layout.addLayout(main_layout)

        # 辅助视频选择
        aux_layout = QHBoxLayout()
        aux_layout.addWidget(QLabel("辅助视频:"))
        self.aux_video_input = QLineEdit()
        self.aux_video_input.setPlaceholderText("选择辅助视频文件...")
        aux_layout.addWidget(self.aux_video_input)

        self.aux_video_btn = QPushButton("浏览")
        self.aux_video_btn.clicked.connect(self.select_aux_video)
        aux_layout.addWidget(self.aux_video_btn)
        layout.addLayout(aux_layout)

        return group
    
    def create_preset_selection_group(self) -> QGroupBox:
        """创建预设选择组"""
        group = QGroupBox("预设模板")
        layout = QVBoxLayout(group)
        
        # 预设选择
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("选择预设:"))
        
        self.preset_combo = QComboBox()
        self.preset_combo.currentIndexChanged.connect(self.on_preset_index_changed)
        preset_layout.addWidget(self.preset_combo)
        layout.addLayout(preset_layout)
        
        # 预设描述
        self.preset_description = QLabel("选择一个预设模板以查看详细信息")
        self.preset_description.setWordWrap(True)
        self.preset_description.setStyleSheet("color: #ABB2BF; font-style: italic; margin: 5px;")
        layout.addWidget(self.preset_description)
        
        return group
    
    def create_output_settings_group(self) -> QGroupBox:
        """创建输出设置组"""
        group = QGroupBox("输出设置")
        layout = QVBoxLayout(group)
        
        # 输出路径选择
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出文件:"))
        
        self.output_input = QLineEdit()
        self.output_input.setPlaceholderText("选择输出文件路径...")
        output_layout.addWidget(self.output_input)
        
        self.output_btn = QPushButton("浏览")
        self.output_btn.clicked.connect(self.select_output_file)
        output_layout.addWidget(self.output_btn)
        layout.addLayout(output_layout)
        
        return group
    
    def create_processing_control_group(self) -> QGroupBox:
        """创建处理控制组"""
        group = QGroupBox("处理控制")
        layout = QVBoxLayout(group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始处理")
        self.start_btn.clicked.connect(self.start_processing)
        self.start_btn.setStyleSheet("QPushButton { background-color: #98C379; color: white; font-weight: bold; }")
        button_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止处理")
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("QPushButton { background-color: #E06C75; color: white; font-weight: bold; }")
        button_layout.addWidget(self.stop_btn)
        
        layout.addLayout(button_layout)
        
        return group
    
    def create_log_panel(self) -> QWidget:
        """创建日志面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 日志标题
        log_title = QLabel("处理日志")
        log_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #E5C07B; margin-bottom: 5px;")
        layout.addWidget(log_title)
        
        # 日志文本区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1E2025;
                border: 1px solid #303238;
                border-radius: 4px;
                color: #ABB2BF;
                font-family: 'Consolas', monospace;
                font-size: 12px;
                padding: 8px;
            }
        """)
        layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_button_layout = QHBoxLayout()
        
        clear_log_btn = QPushButton("清空日志")
        clear_log_btn.clicked.connect(self.clear_log)
        log_button_layout.addWidget(clear_log_btn)
        
        save_log_btn = QPushButton("保存日志")
        save_log_btn.clicked.connect(self.save_log)
        log_button_layout.addWidget(save_log_btn)
        
        log_button_layout.addStretch()
        layout.addLayout(log_button_layout)
        
        return panel
    
    def init_components(self):
        """初始化组件"""
        try:
            # 初始化视频交错器
            self.interleaver = VideoInterleaver()
            
            # 初始化预设管理器
            self.preset_manager = PresetManager()
            
            # 加载预设到下拉框
            self.load_presets()
            
            self.logger.info("组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
            QMessageBox.critical(self, "初始化错误", f"组件初始化失败: {e}")
    
    def setup_logging(self):
        """设置日志记录"""
        # 创建GUI日志处理器
        gui_handler = GuiLogHandler(self.append_log)
        gui_handler.setLevel(logging.INFO)
        
        # 添加到根日志记录器
        root_logger = logging.getLogger()
        root_logger.addHandler(gui_handler)
    
    def load_presets(self):
        """加载预设模板"""
        try:
            presets = self.preset_manager.get_preset_list()
            self.preset_combo.clear()

            # 添加中文显示的预设
            for preset_key in presets:
                preset_info = self.preset_manager.get_preset_info(preset_key)
                display_name = preset_info['name']  # 使用中文名称
                self.preset_combo.addItem(display_name, preset_key)  # 显示名称，数据为key

            if presets:
                # 默认选择寒星AB预设
                for i in range(self.preset_combo.count()):
                    if self.preset_combo.itemData(i) == "hanxing_ab":
                        self.preset_combo.setCurrentIndex(i)
                        self.on_preset_changed("hanxing_ab")
                        break

        except Exception as e:
            self.logger.error(f"加载预设失败: {e}")
    
    def apply_dark_theme(self):
        """应用深色主题"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #282C34;
                color: #ABB2BF;
            }
            QGroupBox {
                color: #ABB2BF;
                font-size: 14px;
                border: 1px solid #303238;
                border-radius: 8px;
                margin-top: 16px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
                color: #61AFEF;
                font-weight: bold;
            }
            QLineEdit, QComboBox {
                background-color: #1E2025;
                border: 1px solid #303238;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
                color: #D0D0D0;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #61AFEF;
            }
            QPushButton {
                background-color: #3A3D44;
                color: #ABB2BF;
                border: none;
                border-radius: 4px;
                font-size: 14px;
                padding: 8px 16px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #4A4D54;
            }
            QPushButton:pressed {
                background-color: #2A2D34;
            }
            QProgressBar {
                border: 1px solid #303238;
                border-radius: 4px;
                text-align: center;
                background-color: #1E2025;
            }
            QProgressBar::chunk {
                background-color: #98C379;
                border-radius: 3px;
            }
            QLabel {
                color: #ABB2BF;
            }
        """)
    
    def select_main_video(self):
        """选择主视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择主视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v);;所有文件 (*)"
        )

        if file_path:
            self.main_video_input.setText(file_path)
            self.append_log(f"已选择主视频: {os.path.basename(file_path)}")

    def select_aux_video(self):
        """选择辅助视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择辅助视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v);;所有文件 (*)"
        )

        if file_path:
            self.aux_video_input.setText(file_path)
            self.append_log(f"已选择辅助视频: {os.path.basename(file_path)}")

    def select_output_file(self):
        """选择输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "选择输出文件",
            "",
            "MP4文件 (*.mp4);;所有文件 (*)"
        )

        if file_path:
            # 确保文件扩展名为.mp4
            if not file_path.lower().endswith('.mp4'):
                file_path += '.mp4'

            self.output_input.setText(file_path)
            self.append_log(f"输出文件: {os.path.basename(file_path)}")

    def on_preset_index_changed(self, index: int):
        """预设索引改变事件"""
        try:
            if index >= 0 and self.preset_manager:
                preset_key = self.preset_combo.itemData(index)
                if preset_key:
                    self.on_preset_changed(preset_key)
        except Exception as e:
            self.logger.warning(f"预设索引改变处理失败: {e}")

    def on_preset_changed(self, preset_name: str):
        """预设改变事件"""
        try:
            if preset_name and self.preset_manager:
                preset_info = self.preset_manager.get_preset_info(preset_name)
                description = f"{preset_info['name']}: {preset_info['description']}"
                self.preset_description.setText(description)
                self.append_log(f"选择预设: {preset_info['name']}")
        except Exception as e:
            self.logger.warning(f"获取预设信息失败: {e}")

    def validate_inputs(self) -> bool:
        """验证输入参数"""
        # 检查主视频
        main_video = self.main_video_input.text().strip()
        if not main_video:
            QMessageBox.warning(self, "输入错误", "请选择主视频文件")
            return False

        if not os.path.exists(main_video):
            QMessageBox.warning(self, "文件错误", "主视频文件不存在")
            return False

        # 检查辅助视频
        aux_video = self.aux_video_input.text().strip()

        if not aux_video:
            QMessageBox.warning(self, "输入错误", "请选择辅助视频文件")
            return False

        if not os.path.exists(aux_video):
            QMessageBox.warning(self, "文件错误", "辅助视频文件不存在")
            return False

        # 检查输出路径
        output_path = self.output_input.text().strip()
        if not output_path:
            QMessageBox.warning(self, "输入错误", "请选择输出文件路径")
            return False

        # 检查输出目录是否存在
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            QMessageBox.warning(self, "路径错误", "输出目录不存在")
            return False

        return True

    def start_processing(self):
        """开始处理"""
        if not self.validate_inputs():
            return

        try:
            # 获取输入参数
            main_video = self.main_video_input.text().strip()
            aux_video = self.aux_video_input.text().strip()
            aux_videos = [aux_video]  # 只有一个辅助视频

            output_path = self.output_input.text().strip()
            # 获取当前选中预设的key值
            current_index = self.preset_combo.currentIndex()
            preset = self.preset_combo.itemData(current_index) if current_index >= 0 else "copyright_protection"

            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # 创建并启动处理线程
            self.processing_thread = ProcessingThread(
                self.interleaver, main_video, aux_videos, output_path, preset
            )

            self.processing_thread.progress_updated.connect(self.update_progress)
            self.processing_thread.log_message.connect(self.append_log)
            self.processing_thread.finished_signal.connect(self.on_processing_finished)

            self.processing_thread.start()

            self.append_log("=" * 50)
            self.append_log("开始视频交错处理...")

        except Exception as e:
            self.logger.error(f"启动处理失败: {e}")
            QMessageBox.critical(self, "处理错误", f"启动处理失败: {e}")
            self.reset_ui_state()

    def stop_processing(self):
        """停止处理"""
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.terminate()
            self.processing_thread.wait()
            self.append_log("⚠️ 处理已被用户停止")

        self.reset_ui_state()

    def update_progress(self, value: int):
        """更新进度条"""
        self.progress_bar.setValue(value)

    def on_processing_finished(self, success: bool, result: str):
        """处理完成回调"""
        self.reset_ui_state()

        if success:
            self.append_log("=" * 50)
            self.append_log("🎉 处理完成！")

            # 询问是否打开输出文件夹
            reply = QMessageBox.question(
                self,
                "处理完成",
                f"视频交错处理完成！\n\n输出文件: {result}\n\n是否打开输出文件夹？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.open_output_folder(result)
        else:
            QMessageBox.critical(self, "处理失败", f"视频交错处理失败:\n{result}")

    def reset_ui_state(self):
        """重置UI状态"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.progress_bar.setValue(0)

    def open_output_folder(self, file_path: str):
        """打开输出文件夹"""
        try:
            import subprocess
            import platform

            folder_path = os.path.dirname(file_path)

            if platform.system() == "Windows":
                subprocess.run(["explorer", folder_path])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])

        except Exception as e:
            self.logger.warning(f"打开文件夹失败: {e}")

    def append_log(self, message: str):
        """添加日志消息"""
        self.log_text.append(message)
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.append_log("日志已清空")

    def save_log(self):
        """保存日志"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存日志文件",
            f"video_interleave_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())

                QMessageBox.information(self, "保存成功", f"日志已保存到: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存日志失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止正在运行的处理线程
        if self.processing_thread and self.processing_thread.isRunning():
            reply = QMessageBox.question(
                self,
                "确认退出",
                "正在处理视频，确定要退出吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.processing_thread.terminate()
                self.processing_thread.wait()
            else:
                event.ignore()
                return

        # 清理资源
        if self.interleaver:
            self.interleaver.cleanup()

        self.logger.info("主窗口关闭")
        event.accept()
