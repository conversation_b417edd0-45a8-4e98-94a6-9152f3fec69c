2025-07-27 23:02:59 - test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:02:59 - test - INFO - 日志系统测试成功
2025-07-27 23:04:44 - test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:04:44 - test - INFO - 日志系统测试成功
2025-07-27 23:07:51 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:07:51 - root - INFO - 应用程序启动
2025-07-27 23:07:51 - root - ERROR - 应用程序运行时发生错误: type object 'ApplicationAttribute' has no attribute 'AA_EnableHighDpiScaling'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\main.py", line 105, in main
    app = setup_application()
          ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\main.py", line 29, in setup_application
    app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'ApplicationAttribute' has no attribute 'AA_EnableHighDpiScaling'
2025-07-27 23:08:36 - test - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:08:36 - test - INFO - 日志系统测试成功
2025-07-27 23:08:45 - root - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:08:45 - root - INFO - 应用程序启动
2025-07-27 23:08:45 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:08:45 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:08:45 - VideoInterleaver - INFO - 配置文件加载成功: C:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-27 23:08:45 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-27 23:08:45 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:08:45 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:08:45 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:08:45 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:08:46 - root - INFO - 主窗口已显示
2025-07-27 23:09:49 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 2 个辅助视频
2025-07-27 23:14:13 - root - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:14:13 - root - INFO - 应用程序启动
2025-07-27 23:14:13 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:14:13 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:14:13 - VideoInterleaver - INFO - 配置文件加载成功: C:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-27 23:14:13 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-27 23:14:13 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:14:13 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:14:13 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:14:13 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:14:14 - root - INFO - 主窗口已显示
2025-07-27 23:15:12 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 2 个辅助视频
2025-07-27 23:15:12 - VideoInterleaver - INFO - 使用预设: steganography
2025-07-27 23:15:12 - VideoInterleaver - INFO - 视频1: C:/Users/<USER>/Desktop/Video/合金三轮车模型 #三轮车 #合金三轮车摸型 #儿童玩具 #好物分享-快手.mp4
2025-07-27 23:15:12 - VideoInterleaver - INFO - 视频2: C:/Users/<USER>/Desktop/Video/大学生！别走！行李箱你就这么选，有毛病算我的！#行李箱-快手.mp4
2025-07-27 23:15:12 - VideoInterleaver - INFO - 视频3: C:/Users/<USER>/Desktop/Video/磨泥器真是太实用了，用它磨土豆或者玉米，可好使了#美食教程 #玉米浆包 #玉米浆包饼-快手.mp4
2025-07-27 23:15:12 - VideoInterleaver - INFO - 开始标准化视频参数...
2025-07-27 23:15:12 - VideoInterleaver - ERROR - 获取视频信息失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-27 23:15:12 - VideoInterleaver - ERROR - 视频交错失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-27 23:15:12 - VideoInterleaver - ERROR - 错误详情: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 260, in interleave_videos
    normalized_videos = self.normalize_videos(all_videos)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 166, in normalize_videos
    info = self.get_video_info(path)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 125, in get_video_info
    info = json.loads(result.stdout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\__init__.py", line 339, in loads
    raise TypeError(f'the JSON object must be str, bytes or bytearray, '
TypeError: the JSON object must be str, bytes or bytearray, not NoneType

2025-07-27 23:20:33 - root - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:20:33 - root - INFO - 应用程序启动
2025-07-27 23:20:33 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:20:33 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:20:33 - VideoInterleaver - INFO - 配置文件加载成功: C:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-27 23:20:34 - VideoInterleaver - INFO - FFmpeg 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-27 23:20:34 - VideoInterleaver - INFO - FFprobe 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-27 23:20:34 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-27 23:20:34 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:20:34 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:20:34 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:20:34 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:20:35 - root - INFO - 主窗口已显示
2025-07-27 23:21:23 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 1 个辅助视频
2025-07-27 23:21:23 - VideoInterleaver - INFO - 使用预设: copyright_protection
2025-07-27 23:21:23 - VideoInterleaver - INFO - 视频1: C:/Users/<USER>/Desktop/封面合成/这个油污净真的使用范围太广了，什么都可以洗，你们有需要的赶紧囤货了__记录我的农村生活-快手_cover_20250727_202632_0d13112c.mp4
2025-07-27 23:21:23 - VideoInterleaver - INFO - 视频2: C:/Users/<USER>/Desktop/辅助视频/time_1.mp4
2025-07-27 23:21:23 - VideoInterleaver - INFO - 开始标准化视频参数...
2025-07-27 23:21:23 - VideoInterleaver - INFO - 执行FFprobe命令: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/这个油污净真的使用范围太广了，什么都可以洗，你们有需要的赶紧囤货了__记录我的农村生活-快手_cover_20250727_202632_0d13112c.mp4
2025-07-27 23:21:23 - VideoInterleaver - INFO - 视频信息 C:/Users/<USER>/Desktop/封面合成/这个油污净真的使用范围太广了，什么都可以洗，你们有需要的赶紧囤货了__记录我的农村生活-快手_cover_20250727_202632_0d13112c.mp4: 720x1280 @ 30.00fps
2025-07-27 23:21:23 - VideoInterleaver - INFO - 执行FFprobe命令: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/辅助视频/time_1.mp4
2025-07-27 23:21:23 - VideoInterleaver - INFO - 视频信息 C:/Users/<USER>/Desktop/辅助视频/time_1.mp4: 1080x1920 @ 25.00fps
2025-07-27 23:21:23 - VideoInterleaver - INFO - 目标参数: 720x1280 @ 30.00fps
2025-07-27 23:21:23 - VideoInterleaver - INFO - 视频 1 参数已匹配，无需转换
2025-07-27 23:21:25 - VideoInterleaver - INFO - 视频参数转换完成: C:\Users\<USER>\AppData\Local\Temp\video_interleave_q6zosf65\normalized_1.mp4
2025-07-27 23:21:25 - VideoInterleaver - INFO - 视频 2 已标准化: C:\Users\<USER>\AppData\Local\Temp\video_interleave_q6zosf65\normalized_1.mp4
2025-07-27 23:21:25 - VideoInterleaver - INFO - 构建FFmpeg命令...
2025-07-27 23:21:25 - VideoInterleaver - INFO - 执行FFmpeg命令: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:/Users/<USER>/Desktop/封面合成/这个油污净真的使用范围太广了，什么都可以洗，你们有需要的赶紧囤货了__记录我的农村生活-快手_cover_20250727_202632_0d13112c.mp4 -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_q6zosf65\normalized_1.mp4 -filter_complex [0:v]fps=30[v0];[1:v]fps=30[v1];[v0][v1]interleave=nb_inputs=2:duration=longest[out] -map [out] -c:v h264 -preset slow -crf 18 -keyint_min 1 -g 3 -bf 2 -b_strategy 2 -y C:/Users/<USER>/Desktop/1.mp4
2025-07-27 23:22:55 - VideoInterleaver - INFO - 视频交错完成: C:/Users/<USER>/Desktop/1.mp4 (大小: 426312641 字节)
2025-07-27 23:27:10 - root - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:27:10 - root - INFO - 应用程序启动
2025-07-27 23:27:10 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:27:10 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:27:10 - VideoInterleaver - INFO - 配置文件加载成功: C:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-27 23:27:10 - VideoInterleaver - INFO - FFmpeg 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-27 23:27:10 - VideoInterleaver - INFO - FFprobe 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-27 23:27:10 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-27 23:27:10 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:27:10 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:27:10 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:27:10 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:27:11 - root - INFO - 主窗口已显示
2025-07-27 23:27:53 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:27:53 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:27:53 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:27:53 - MainWindow - INFO - 主窗口关闭
2025-07-27 23:27:53 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:27:53 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:27:53 - MainWindow - INFO - 主窗口关闭
2025-07-27 23:29:08 - root - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:29:08 - root - INFO - 应用程序启动
2025-07-27 23:29:08 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:29:08 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:29:08 - VideoInterleaver - INFO - 配置文件加载成功: C:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-27 23:29:08 - VideoInterleaver - INFO - FFmpeg 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-27 23:29:08 - VideoInterleaver - INFO - FFprobe 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-27 23:29:08 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-27 23:29:08 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:29:08 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:29:08 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:29:08 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:29:09 - root - INFO - 主窗口已显示
2025-07-27 23:29:43 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:29:43 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:29:43 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:29:43 - MainWindow - INFO - 主窗口关闭
2025-07-27 23:30:53 - root - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:30:53 - root - INFO - 应用程序启动
2025-07-27 23:30:53 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:30:53 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:30:53 - VideoInterleaver - INFO - 配置文件加载成功: C:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-27 23:30:53 - VideoInterleaver - INFO - FFmpeg 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-27 23:30:53 - VideoInterleaver - INFO - FFprobe 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-27 23:30:53 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-27 23:30:53 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:30:53 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:30:53 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:30:53 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:30:54 - root - INFO - 主窗口已显示
2025-07-27 23:33:31 - root - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:33:31 - root - INFO - 应用程序启动
2025-07-27 23:33:31 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:33:31 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:33:31 - VideoInterleaver - INFO - 配置文件加载成功: C:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-27 23:33:31 - VideoInterleaver - INFO - FFmpeg 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-27 23:33:31 - VideoInterleaver - INFO - FFprobe 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-27 23:33:31 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-27 23:33:31 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:33:31 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:33:31 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:33:31 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:33:32 - root - INFO - 主窗口已显示
2025-07-27 23:34:06 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250727.log
2025-07-27 23:34:06 - MainWindow - INFO - 组件初始化完成
2025-07-27 23:34:06 - MainWindow - INFO - 主窗口初始化完成
2025-07-27 23:34:06 - MainWindow - INFO - 主窗口关闭
2025-07-27 23:52:18 - VideoInterleaver - INFO - 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\video_interleave_wqx1gimw
2025-07-27 23:52:18 - MainWindow - INFO - 主窗口关闭
2025-07-27 23:52:18 - MainWindow - INFO - 主窗口关闭
2025-07-27 23:52:18 - root - INFO - 应用程序退出，退出码: 0
