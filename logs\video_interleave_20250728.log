2025-07-28 00:00:26 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 00:00:26 - root - INFO - 应用程序启动
2025-07-28 00:00:26 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 00:00:26 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 00:00:26 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 00:00:26 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 00:00:26 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 00:00:26 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 00:00:26 - MainWindow - INFO - 组件初始化完成
2025-07-28 00:00:26 - MainWindow - INFO - 组件初始化完成
2025-07-28 00:00:26 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 00:00:26 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 00:00:27 - root - INFO - 主窗口已显示
2025-07-28 00:00:53 - VideoInterleaver - INFO - 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\video_interleave_g_80jhe4
2025-07-28 00:00:53 - MainWindow - INFO - 主窗口关闭
2025-07-28 00:00:53 - MainWindow - INFO - 主窗口关闭
2025-07-28 00:00:53 - root - INFO - 应用程序退出，退出码: 0
2025-07-28 00:00:57 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 00:00:57 - root - INFO - 应用程序启动
2025-07-28 00:00:57 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 00:00:57 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 00:00:57 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 00:00:57 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 00:00:57 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 00:00:57 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 00:00:57 - MainWindow - INFO - 组件初始化完成
2025-07-28 00:00:57 - MainWindow - INFO - 组件初始化完成
2025-07-28 00:00:57 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 00:00:57 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 00:00:58 - root - INFO - 主窗口已显示
2025-07-28 00:02:53 - VideoInterleaver - INFO - 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\video_interleave_cdjrkdgx
2025-07-28 00:02:53 - MainWindow - INFO - 主窗口关闭
2025-07-28 00:02:53 - MainWindow - INFO - 主窗口关闭
2025-07-28 00:02:53 - root - INFO - 应用程序退出，退出码: 0
2025-07-28 00:04:08 - root - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 00:04:08 - root - INFO - 应用程序启动
2025-07-28 00:04:08 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 00:04:08 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 00:04:09 - VideoInterleaver - INFO - 配置文件加载成功: C:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 00:04:09 - VideoInterleaver - INFO - FFmpeg 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 00:04:09 - VideoInterleaver - INFO - FFprobe 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 00:04:09 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 00:04:09 - MainWindow - INFO - 组件初始化完成
2025-07-28 00:04:09 - MainWindow - INFO - 组件初始化完成
2025-07-28 00:04:09 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 00:04:09 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 00:04:09 - root - INFO - 主窗口已显示
2025-07-28 00:04:47 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 00:04:47 - MainWindow - INFO - 组件初始化完成
2025-07-28 00:04:47 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 00:04:47 - MainWindow - INFO - 主窗口关闭
2025-07-28 01:09:01 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:09:01 - root - INFO - 应用程序启动
2025-07-28 01:09:01 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:09:01 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:09:01 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 01:09:01 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 01:09:01 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 01:09:01 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 01:09:01 - MainWindow - INFO - 组件初始化完成
2025-07-28 01:09:01 - MainWindow - INFO - 组件初始化完成
2025-07-28 01:09:01 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 01:09:01 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 01:09:02 - root - INFO - 主窗口已显示
2025-07-28 01:16:15 - VideoInterleaver - INFO - 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\video_interleave_yypeazfa
2025-07-28 01:16:15 - MainWindow - INFO - 主窗口关闭
2025-07-28 01:16:15 - MainWindow - INFO - 主窗口关闭
2025-07-28 01:16:15 - root - INFO - 应用程序退出，退出码: 0
2025-07-28 01:17:40 - interleave_test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:18:58 - demo - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:18:58 - demo - INFO - 视频交错器初始化成功
2025-07-28 01:20:08 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:20:08 - root - INFO - 应用程序启动
2025-07-28 01:20:08 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:20:08 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:20:08 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 01:20:08 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 01:20:08 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 01:20:08 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 01:20:08 - MainWindow - INFO - 组件初始化完成
2025-07-28 01:20:08 - MainWindow - INFO - 组件初始化完成
2025-07-28 01:20:08 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 01:20:08 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 01:20:09 - root - INFO - 主窗口已显示
2025-07-28 01:21:08 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 01:21:08 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 01:21:08 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/封面合成/平底锅__炒锅__锅__我要上热门O3x8er38dpbhvbaa__快手卖货助手O3x5yurai_cover_20250727_202435_e834d0dd.mp4
2025-07-28 01:21:08 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_19.mp4
2025-07-28 01:21:08 - VideoInterleaver - INFO - 开始匹配视频时长...
2025-07-28 01:21:08 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/平底锅__炒锅__锅__我要上热门O3x8er38dpbhvbaa__快手卖货助手O3x5yurai_cover_20250727_202435_e834d0dd.mp4
2025-07-28 01:21:08 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/辅助视频/time_19.mp4
2025-07-28 01:21:08 - VideoInterleaver - INFO - 主视频时长: 100.36秒
2025-07-28 01:21:08 - VideoInterleaver - INFO - 辅助视频时长: 7.02秒
2025-07-28 01:21:08 - VideoInterleaver - INFO - 辅助视频较短，进行循环处理...
2025-07-28 01:21:49 - VideoInterleaver - INFO - 视频循环处理完成: C:\Users\<USER>\AppData\Local\Temp\video_interleave_xzndlxvi\processed_aux.mp4
2025-07-28 01:21:49 - VideoInterleaver - INFO - 开始标准化视频参数...
2025-07-28 01:21:49 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/平底锅__炒锅__锅__我要上热门O3x8er38dpbhvbaa__快手卖货助手O3x5yurai_cover_20250727_202435_e834d0dd.mp4
2025-07-28 01:21:49 - VideoInterleaver - INFO - 视频信息 C:/Users/<USER>/Desktop/封面合成/平底锅__炒锅__锅__我要上热门O3x8er38dpbhvbaa__快手卖货助手O3x5yurai_cover_20250727_202435_e834d0dd.mp4: 720x1280 @ 30.00fps
2025-07-28 01:21:49 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:\Users\<USER>\AppData\Local\Temp\video_interleave_xzndlxvi\processed_aux.mp4
2025-07-28 01:21:49 - VideoInterleaver - INFO - 视频信息 C:\Users\<USER>\AppData\Local\Temp\video_interleave_xzndlxvi\processed_aux.mp4: 1080x1920 @ 25.00fps
2025-07-28 01:21:49 - VideoInterleaver - INFO - 目标参数: 720x1280 @ 30.00fps
2025-07-28 01:21:49 - VideoInterleaver - INFO - 视频 1 参数已匹配，无需转换
2025-07-28 01:24:37 - crash_test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:24:37 - crash_test - INFO - 视频交错器初始化成功
2025-07-28 01:24:39 - error_test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:29:28 - gpu_test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:29:28 - benchmark - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:32:56 - gpu_demo - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:34:20 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:34:20 - root - INFO - 应用程序启动
2025-07-28 01:34:20 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:34:20 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:34:20 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 01:34:22 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 01:34:22 - VideoInterleaver - INFO - GPU加速已启用: NVIDIA GeForce GTX 1060 3GB: h264_nvenc, hevc_nvenc
2025-07-28 01:34:22 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 01:34:22 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 01:34:22 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 01:34:22 - MainWindow - INFO - 组件初始化完成
2025-07-28 01:34:22 - MainWindow - INFO - 组件初始化完成
2025-07-28 01:34:22 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 01:34:22 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 01:34:23 - root - INFO - 主窗口已显示
2025-07-28 01:35:00 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 01:35:00 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 01:35:00 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/封面合成/这个油污净真的使用范围太广了，什么都可以洗，你们有需要的赶紧囤货了__记录我的农村生活-快手_cover_20250727_202632_0d13112c.mp4
2025-07-28 01:35:00 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_11.mp4
2025-07-28 01:35:00 - VideoInterleaver - INFO - 开始匹配视频时长...
2025-07-28 01:35:00 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/这个油污净真的使用范围太广了，什么都可以洗，你们有需要的赶紧囤货了__记录我的农村生活-快手_cover_20250727_202632_0d13112c.mp4
2025-07-28 01:35:00 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/辅助视频/time_11.mp4
2025-07-28 01:35:01 - VideoInterleaver - INFO - 主视频时长: 259.66秒
2025-07-28 01:35:01 - VideoInterleaver - INFO - 辅助视频时长: 7.42秒
2025-07-28 01:35:01 - VideoInterleaver - INFO - 辅助视频较短，进行循环处理...
2025-07-28 01:36:54 - VideoInterleaver - INFO - 视频循环处理完成: C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4
2025-07-28 01:36:54 - VideoInterleaver - INFO - 开始标准化视频参数...
2025-07-28 01:36:54 - VideoInterleaver - INFO - 获取视频 1 信息: C:/Users/<USER>/Desktop/封面合成/这个油污净真的使用范围太广了，什么都可以洗，你们有需要的赶紧囤货了__记录我的农村生活-快手_cover_20250727_202632_0d13112c.mp4
2025-07-28 01:36:54 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/这个油污净真的使用范围太广了，什么都可以洗，你们有需要的赶紧囤货了__记录我的农村生活-快手_cover_20250727_202632_0d13112c.mp4
2025-07-28 01:36:54 - VideoInterleaver - INFO - 视频信息 C:/Users/<USER>/Desktop/封面合成/这个油污净真的使用范围太广了，什么都可以洗，你们有需要的赶紧囤货了__记录我的农村生活-快手_cover_20250727_202632_0d13112c.mp4: 720x1280 @ 30.00fps
2025-07-28 01:36:54 - VideoInterleaver - INFO - 获取视频 2 信息: C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4
2025-07-28 01:36:54 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4
2025-07-28 01:36:54 - VideoInterleaver - INFO - 视频信息 C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4: 1080x1920 @ 25.00fps
2025-07-28 01:36:54 - VideoInterleaver - INFO - 目标参数: 720x1280 @ 30.00fps
2025-07-28 01:36:54 - VideoInterleaver - INFO - 视频 1 参数已匹配，无需转换
2025-07-28 01:36:54 - VideoInterleaver - INFO - 视频 2 需要转换参数
2025-07-28 01:36:54 - VideoInterleaver - INFO - 使用GPU加速进行视频参数转换
2025-07-28 01:36:54 - VideoInterleaver - INFO - 开始转换视频参数: C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4 -> C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\normalized_1.mp4
2025-07-28 01:36:54 - VideoInterleaver - INFO - 目标参数: 720x1280 @ 30.0fps
2025-07-28 01:36:54 - VideoInterleaver - INFO - 执行FFmpeg命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4 -vf scale=720:1280,fps=30.0 -c:v h264_nvenc -preset p4 -cq 20 -rc cq -spatial_aq 1 -temporal_aq 1 -gpu 0 -c:a copy -y C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\normalized_1.mp4
2025-07-28 01:36:54 - VideoInterleaver - ERROR - 视频参数转换失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:04:19.68, start: 0.000000, bitrate: 6280 kb/s
  Stream #0:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 6145 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (h264_nvenc))
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
[h264_nvenc @ 000001ccb9185c40] [Eval @ 000000082e5fea20] Undefined constant or missing '(' in 'cq'
[h264_nvenc @ 000001ccb9185c40] Unable to parse option value "cq"
[h264_nvenc @ 000001ccb9185c40] Error setting option rc to value cq.
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Error while opening encoder - maybe incorrect parameters such as bit_rate, rate, width or height.
[vf#0:0 @ 000001ccb9a60ac0] Error sending frames to consumers: Invalid argument
[vf#0:0 @ 000001ccb9a60ac0] Task finished with error code: -22 (Invalid argument)
[vf#0:0 @ 000001ccb9a60ac0] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 000001ccb8c9a700] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!

2025-07-28 01:36:54 - VideoInterleaver - ERROR - FFmpeg返回码: 4294967274
2025-07-28 01:36:54 - VideoInterleaver - ERROR - 标准化视频 2 失败: FFmpeg执行失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:04:19.68, start: 0.000000, bitrate: 6280 kb/s
  Stream #0:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 6145 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (h264_nvenc))
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
[h264_nvenc @ 000001ccb9185c40] [Eval @ 000000082e5fea20] Undefined constant or missing '(' in 'cq'
[h264_nvenc @ 000001ccb9185c40] Unable to parse option value "cq"
[h264_nvenc @ 000001ccb9185c40] Error setting option rc to value cq.
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Error while opening encoder - maybe incorrect parameters such as bit_rate, rate, width or height.
[vf#0:0 @ 000001ccb9a60ac0] Error sending frames to consumers: Invalid argument
[vf#0:0 @ 000001ccb9a60ac0] Task finished with error code: -22 (Invalid argument)
[vf#0:0 @ 000001ccb9a60ac0] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 000001ccb8c9a700] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!

2025-07-28 01:36:54 - VideoInterleaver - ERROR - 视频标准化过程失败: 标准化视频 2 (C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4) 失败: FFmpeg执行失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:04:19.68, start: 0.000000, bitrate: 6280 kb/s
  Stream #0:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 6145 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (h264_nvenc))
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
[h264_nvenc @ 000001ccb9185c40] [Eval @ 000000082e5fea20] Undefined constant or missing '(' in 'cq'
[h264_nvenc @ 000001ccb9185c40] Unable to parse option value "cq"
[h264_nvenc @ 000001ccb9185c40] Error setting option rc to value cq.
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Error while opening encoder - maybe incorrect parameters such as bit_rate, rate, width or height.
[vf#0:0 @ 000001ccb9a60ac0] Error sending frames to consumers: Invalid argument
[vf#0:0 @ 000001ccb9a60ac0] Task finished with error code: -22 (Invalid argument)
[vf#0:0 @ 000001ccb9a60ac0] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 000001ccb8c9a700] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!

2025-07-28 01:36:54 - VideoInterleaver - ERROR - 视频交错失败: 标准化视频 2 (C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4) 失败: FFmpeg执行失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:04:19.68, start: 0.000000, bitrate: 6280 kb/s
  Stream #0:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 6145 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (h264_nvenc))
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
[h264_nvenc @ 000001ccb9185c40] [Eval @ 000000082e5fea20] Undefined constant or missing '(' in 'cq'
[h264_nvenc @ 000001ccb9185c40] Unable to parse option value "cq"
[h264_nvenc @ 000001ccb9185c40] Error setting option rc to value cq.
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Error while opening encoder - maybe incorrect parameters such as bit_rate, rate, width or height.
[vf#0:0 @ 000001ccb9a60ac0] Error sending frames to consumers: Invalid argument
[vf#0:0 @ 000001ccb9a60ac0] Task finished with error code: -22 (Invalid argument)
[vf#0:0 @ 000001ccb9a60ac0] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 000001ccb8c9a700] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!

2025-07-28 01:36:54 - VideoInterleaver - ERROR - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 449, in _convert_video_params
    result = subprocess.run(cmd, capture_output=True, text=True,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['c:\\Users\\<USER>\\Desktop\\7\\video_interleave_project\\bin\\ffmpeg.exe', '-i', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_interleave_ox8srg50\\processed_aux.mp4', '-vf', 'scale=720:1280,fps=30.0', '-c:v', 'h264_nvenc', '-preset', 'p4', '-cq', '20', '-rc', 'cq', '-spatial_aq', '1', '-temporal_aq', '1', '-gpu', '0', '-c:a', 'copy', '-y', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_interleave_ox8srg50\\normalized_1.mp4']' returned non-zero exit status 4294967274.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 267, in normalize_videos
    self._convert_video_params(path, output_path, target_width, target_height, target_fps)
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 465, in _convert_video_params
    raise RuntimeError(f"FFmpeg执行失败: {e.stderr}")
RuntimeError: FFmpeg执行失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:04:19.68, start: 0.000000, bitrate: 6280 kb/s
  Stream #0:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 6145 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (h264_nvenc))
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
[h264_nvenc @ 000001ccb9185c40] [Eval @ 000000082e5fea20] Undefined constant or missing '(' in 'cq'
[h264_nvenc @ 000001ccb9185c40] Unable to parse option value "cq"
[h264_nvenc @ 000001ccb9185c40] Error setting option rc to value cq.
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Error while opening encoder - maybe incorrect parameters such as bit_rate, rate, width or height.
[vf#0:0 @ 000001ccb9a60ac0] Error sending frames to consumers: Invalid argument
[vf#0:0 @ 000001ccb9a60ac0] Task finished with error code: -22 (Invalid argument)
[vf#0:0 @ 000001ccb9a60ac0] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 000001ccb8c9a700] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 517, in interleave_videos
    normalized_videos = self.normalize_videos(all_videos)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 273, in normalize_videos
    raise RuntimeError(f"标准化视频 {i+1} ({path}) 失败: {e}")
RuntimeError: 标准化视频 2 (C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4) 失败: FFmpeg执行失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:04:19.68, start: 0.000000, bitrate: 6280 kb/s
  Stream #0:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 6145 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (h264_nvenc))
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
[h264_nvenc @ 000001ccb9185c40] [Eval @ 000000082e5fea20] Undefined constant or missing '(' in 'cq'
[h264_nvenc @ 000001ccb9185c40] Unable to parse option value "cq"
[h264_nvenc @ 000001ccb9185c40] Error setting option rc to value cq.
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Error while opening encoder - maybe incorrect parameters such as bit_rate, rate, width or height.
[vf#0:0 @ 000001ccb9a60ac0] Error sending frames to consumers: Invalid argument
[vf#0:0 @ 000001ccb9a60ac0] Task finished with error code: -22 (Invalid argument)
[vf#0:0 @ 000001ccb9a60ac0] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 000001ccb8c9a700] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!


2025-07-28 01:39:11 - gpu_fix_test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:39:13 - integration_test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:39:44 - gpu_fix_test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:39:47 - integration_test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:40:55 - VideoInterleaver - INFO - 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50
2025-07-28 01:40:55 - MainWindow - INFO - 主窗口关闭
2025-07-28 01:40:55 - MainWindow - INFO - 主窗口关闭
2025-07-28 01:40:55 - root - INFO - 应用程序退出，退出码: 0
2025-07-28 01:41:50 - simple_gpu_test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:42:28 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:42:28 - root - INFO - 应用程序启动
2025-07-28 01:42:28 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:42:28 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:42:28 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 01:42:29 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 01:42:29 - VideoInterleaver - INFO - GPU加速已启用: NVIDIA GeForce GTX 1060 3GB: h264_nvenc, hevc_nvenc
2025-07-28 01:42:29 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 01:42:29 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 01:42:29 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 01:42:29 - MainWindow - INFO - 组件初始化完成
2025-07-28 01:42:29 - MainWindow - INFO - 组件初始化完成
2025-07-28 01:42:29 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 01:42:29 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 01:42:30 - root - INFO - 主窗口已显示
2025-07-28 01:42:32 - VideoInterleaver - INFO - 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\video_interleave_scvhfkwe
2025-07-28 01:42:32 - MainWindow - INFO - 主窗口关闭
2025-07-28 01:42:32 - MainWindow - INFO - 主窗口关闭
2025-07-28 01:42:32 - root - INFO - 应用程序退出，退出码: 0
2025-07-28 01:42:39 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:42:39 - root - INFO - 应用程序启动
2025-07-28 01:42:39 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:42:39 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:42:39 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 01:42:40 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 01:42:40 - VideoInterleaver - INFO - GPU加速已启用: NVIDIA GeForce GTX 1060 3GB: h264_nvenc, hevc_nvenc
2025-07-28 01:42:40 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 01:42:40 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 01:42:40 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 01:42:40 - MainWindow - INFO - 组件初始化完成
2025-07-28 01:42:40 - MainWindow - INFO - 组件初始化完成
2025-07-28 01:42:40 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 01:42:40 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 01:42:41 - root - INFO - 主窗口已显示
2025-07-28 01:44:34 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 01:44:34 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 01:44:34 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4
2025-07-28 01:44:34 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_10.mp4
2025-07-28 01:44:34 - VideoInterleaver - INFO - 开始匹配视频时长...
2025-07-28 01:44:34 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4
2025-07-28 01:44:34 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/辅助视频/time_10.mp4
2025-07-28 01:44:34 - VideoInterleaver - INFO - 主视频时长: 66.29秒
2025-07-28 01:44:34 - VideoInterleaver - INFO - 辅助视频时长: 7.62秒
2025-07-28 01:44:34 - VideoInterleaver - INFO - 辅助视频较短，进行循环处理...
2025-07-28 01:45:03 - VideoInterleaver - INFO - 视频循环处理完成: C:\Users\<USER>\AppData\Local\Temp\video_interleave__lstekol\processed_aux.mp4
2025-07-28 01:45:03 - VideoInterleaver - INFO - 开始标准化视频参数...
2025-07-28 01:45:03 - VideoInterleaver - INFO - 获取视频 1 信息: C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4
2025-07-28 01:45:03 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4
2025-07-28 01:45:03 - VideoInterleaver - INFO - 视频信息 C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4: 648x1128 @ 30.00fps
2025-07-28 01:45:03 - VideoInterleaver - INFO - 获取视频 2 信息: C:\Users\<USER>\AppData\Local\Temp\video_interleave__lstekol\processed_aux.mp4
2025-07-28 01:45:03 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:\Users\<USER>\AppData\Local\Temp\video_interleave__lstekol\processed_aux.mp4
2025-07-28 01:45:03 - VideoInterleaver - INFO - 视频信息 C:\Users\<USER>\AppData\Local\Temp\video_interleave__lstekol\processed_aux.mp4: 1080x1920 @ 25.00fps
2025-07-28 01:45:03 - VideoInterleaver - INFO - 目标参数: 648x1128 @ 30.00fps
2025-07-28 01:45:03 - VideoInterleaver - INFO - 视频 1 参数已匹配，无需转换
2025-07-28 01:45:03 - VideoInterleaver - INFO - 视频 2 需要转换参数
2025-07-28 01:45:03 - VideoInterleaver - INFO - 尝试使用NVENC GPU加速
2025-07-28 01:45:03 - VideoInterleaver - INFO - 执行NVENC命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:\Users\<USER>\AppData\Local\Temp\video_interleave__lstekol\processed_aux.mp4 -vf scale=648:1128,fps=30.0 -c:v h264_nvenc -preset fast -b:v 10000k -maxrate 15000k -bufsize 20000k -c:a copy -y C:\Users\<USER>\AppData\Local\Temp\video_interleave__lstekol\normalized_1.mp4
2025-07-28 01:45:06 - VideoInterleaver - INFO - NVENC视频转换成功: C:\Users\<USER>\AppData\Local\Temp\video_interleave__lstekol\normalized_1.mp4 (大小: 84320894 字节)
2025-07-28 01:45:06 - VideoInterleaver - INFO - 视频 2 已标准化: C:\Users\<USER>\AppData\Local\Temp\video_interleave__lstekol\normalized_1.mp4
2025-07-28 01:45:06 - VideoInterleaver - INFO - 所有视频标准化完成，共处理 2 个视频
2025-07-28 01:45:06 - VideoInterleaver - INFO - 构建FFmpeg命令...
2025-07-28 01:45:06 - VideoInterleaver - INFO - 使用NVENC GPU加速编码
2025-07-28 01:45:06 - VideoInterleaver - INFO - 执行FFmpeg命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4 -i C:\Users\<USER>\AppData\Local\Temp\video_interleave__lstekol\normalized_1.mp4 -filter_complex [0:v]fps=120[a];[1:v]fps=120[b];[a][b]interleave=nb_inputs=2:duration=longest[out] -map [out] -c:v h264_nvenc -preset fast -b:v 50000k -maxrate 50000k -bufsize 100000k -g 3 -r 120 -y C:/Users/<USER>/Desktop/1.mp4
2025-07-28 01:45:07 - VideoInterleaver - ERROR - FFmpeg执行失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:01:06.29, start: 0.076009, bitrate: 14380 kb/s
  Stream #0:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(progressive), 648x1128 [SAR 1:1 DAR 27:47], 14245 kb/s, 30 fps, 30 tbr, 15360 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 h264_nvenc
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 130 kb/s (default)
      Metadata:
        handler_name    : Core Media Audio
        vendor_id       : [0][0][0][0]
Input #1, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave__lstekol\normalized_1.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:01:06.33, start: 0.000000, bitrate: 10169 kb/s
  Stream #1:0[0x1](eng): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(progressive), 648x1128 [SAR 47:48 DAR 9:16], 10033 kb/s, 30 fps, 30 tbr, 15360 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 h264_nvenc
  Stream #1:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 (h264) -> fps:default
  Stream #1:0 (h264) -> fps:default
  interleave:default -> Stream #0:0 (h264_nvenc)
Press [q] to stop, [?] for help
[Parsed_interleave_2 @ 0000021d15530600] Parameters for input link input1 (size 648x1128, SAR 47:48) do not match the corresponding output link parameters (648x1128, SAR 1:1)
[Parsed_interleave_2 @ 0000021d15530600] Failed to configure output pad on Parsed_interleave_2
[fc#0 @ 0000021d108d0800] Error reinitializing filters!
[fc#0 @ 0000021d108d0800] Task finished with error code: -22 (Invalid argument)
[fc#0 @ 0000021d108d0800] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 0000021d10f8bd00] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 0000021d10f8bd00] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 0000021d10f8bd00] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 0000021d11213d80] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!

2025-07-28 01:46:39 - sar_test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:47:18 - VideoInterleaver - INFO - 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\video_interleave__lstekol
2025-07-28 01:47:18 - MainWindow - INFO - 主窗口关闭
2025-07-28 01:47:18 - MainWindow - INFO - 主窗口关闭
2025-07-28 01:47:18 - root - INFO - 应用程序退出，退出码: 0
2025-07-28 01:47:27 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:47:27 - root - INFO - 应用程序启动
2025-07-28 01:47:27 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:47:27 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 01:47:27 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 01:47:28 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 01:47:28 - VideoInterleaver - INFO - GPU加速已启用: NVIDIA GeForce GTX 1060 3GB: h264_nvenc, hevc_nvenc
2025-07-28 01:47:28 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 01:47:28 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 01:47:28 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 01:47:28 - MainWindow - INFO - 组件初始化完成
2025-07-28 01:47:28 - MainWindow - INFO - 组件初始化完成
2025-07-28 01:47:28 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 01:47:28 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 01:47:29 - root - INFO - 主窗口已显示
2025-07-28 01:48:16 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 01:48:16 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 01:48:16 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4
2025-07-28 01:48:16 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_19.mp4
2025-07-28 01:48:16 - VideoInterleaver - INFO - 开始匹配视频时长...
2025-07-28 01:48:16 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4
2025-07-28 01:48:16 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/辅助视频/time_19.mp4
2025-07-28 01:48:16 - VideoInterleaver - INFO - 主视频时长: 66.29秒
2025-07-28 01:48:16 - VideoInterleaver - INFO - 辅助视频时长: 7.02秒
2025-07-28 01:48:16 - VideoInterleaver - INFO - 辅助视频较短，进行循环处理...
2025-07-28 01:48:43 - VideoInterleaver - INFO - 视频循环处理完成: C:\Users\<USER>\AppData\Local\Temp\video_interleave_hw8v8t4d\processed_aux.mp4
2025-07-28 01:48:43 - VideoInterleaver - INFO - 开始标准化视频参数...
2025-07-28 01:48:43 - VideoInterleaver - INFO - 获取视频 1 信息: C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4
2025-07-28 01:48:43 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4
2025-07-28 01:48:43 - VideoInterleaver - INFO - 视频信息 C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4: 648x1128 @ 30.00fps
2025-07-28 01:48:43 - VideoInterleaver - INFO - 获取视频 2 信息: C:\Users\<USER>\AppData\Local\Temp\video_interleave_hw8v8t4d\processed_aux.mp4
2025-07-28 01:48:43 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:\Users\<USER>\AppData\Local\Temp\video_interleave_hw8v8t4d\processed_aux.mp4
2025-07-28 01:48:43 - VideoInterleaver - INFO - 视频信息 C:\Users\<USER>\AppData\Local\Temp\video_interleave_hw8v8t4d\processed_aux.mp4: 1080x1920 @ 25.00fps
2025-07-28 01:48:43 - VideoInterleaver - INFO - 目标参数: 648x1128 @ 30.00fps
2025-07-28 01:48:43 - VideoInterleaver - INFO - 视频 1 参数已匹配，无需转换
2025-07-28 01:48:43 - VideoInterleaver - INFO - 视频 2 需要转换参数
2025-07-28 01:48:43 - VideoInterleaver - INFO - 尝试使用NVENC GPU加速
2025-07-28 01:48:43 - VideoInterleaver - INFO - 执行NVENC命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_hw8v8t4d\processed_aux.mp4 -vf scale=648:1128,fps=30.0,setsar=1 -c:v h264_nvenc -preset fast -b:v 10000k -maxrate 15000k -bufsize 20000k -c:a copy -y C:\Users\<USER>\AppData\Local\Temp\video_interleave_hw8v8t4d\normalized_1.mp4
2025-07-28 01:48:46 - VideoInterleaver - INFO - NVENC视频转换成功: C:\Users\<USER>\AppData\Local\Temp\video_interleave_hw8v8t4d\normalized_1.mp4 (大小: 84361416 字节)
2025-07-28 01:48:46 - VideoInterleaver - INFO - 视频 2 已标准化: C:\Users\<USER>\AppData\Local\Temp\video_interleave_hw8v8t4d\normalized_1.mp4
2025-07-28 01:48:46 - VideoInterleaver - INFO - 所有视频标准化完成，共处理 2 个视频
2025-07-28 01:48:46 - VideoInterleaver - INFO - 构建FFmpeg命令...
2025-07-28 01:48:46 - VideoInterleaver - INFO - 使用NVENC GPU加速编码
2025-07-28 01:48:46 - VideoInterleaver - INFO - 执行FFmpeg命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4 -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_hw8v8t4d\normalized_1.mp4 -filter_complex [0:v]fps=120,setsar=1[a];[1:v]fps=120,setsar=1[b];[a][b]interleave=nb_inputs=2:duration=longest[out] -map [out] -c:v h264_nvenc -preset fast -b:v 50000k -maxrate 50000k -bufsize 100000k -g 3 -r 120 -y C:/Users/<USER>/Desktop/1.mp4
2025-07-28 01:48:52 - VideoInterleaver - INFO - 视频交错完成: C:/Users/<USER>/Desktop/1.mp4 (大小: 414320030 字节)
2025-07-28 02:00:52 - workflow_test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:03:49 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:03:49 - root - INFO - 应用程序启动
2025-07-28 02:03:49 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:03:49 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:03:49 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 02:03:50 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 02:03:50 - VideoInterleaver - INFO - GPU加速已启用: NVIDIA GeForce GTX 1060 3GB: h264_nvenc, hevc_nvenc
2025-07-28 02:03:50 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 02:03:50 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 02:03:50 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 02:03:50 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:03:50 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:03:50 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:03:50 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:03:52 - root - INFO - 主窗口已显示
2025-07-28 02:04:12 - VideoInterleaver - INFO - 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\video_interleave_056rlhd7
2025-07-28 02:04:12 - MainWindow - INFO - 主窗口关闭
2025-07-28 02:04:12 - MainWindow - INFO - 主窗口关闭
2025-07-28 02:04:12 - root - INFO - 应用程序退出，退出码: 0
2025-07-28 02:04:13 - VideoInterleaver - INFO - 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\video_interleave_hw8v8t4d
2025-07-28 02:04:13 - MainWindow - INFO - 主窗口关闭
2025-07-28 02:04:13 - MainWindow - INFO - 主窗口关闭
2025-07-28 02:04:13 - root - INFO - 应用程序退出，退出码: 0
2025-07-28 02:04:16 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:04:16 - root - INFO - 应用程序启动
2025-07-28 02:04:16 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:04:16 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:04:17 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 02:04:18 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 02:04:18 - VideoInterleaver - INFO - GPU加速已启用: NVIDIA GeForce GTX 1060 3GB: h264_nvenc, hevc_nvenc
2025-07-28 02:04:18 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 02:04:18 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 02:04:18 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 02:04:18 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:04:18 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:04:18 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:04:18 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:04:19 - root - INFO - 主窗口已显示
2025-07-28 02:04:54 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 02:04:54 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 02:04:54 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4
2025-07-28 02:04:54 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_11.mp4
2025-07-28 02:04:54 - VideoInterleaver - INFO - 开始匹配视频时长...
2025-07-28 02:04:54 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4
2025-07-28 02:04:54 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/辅助视频/time_11.mp4
2025-07-28 02:04:54 - VideoInterleaver - INFO - 主视频时长: 66.29秒
2025-07-28 02:04:54 - VideoInterleaver - INFO - 辅助视频时长: 7.42秒
2025-07-28 02:04:54 - VideoInterleaver - INFO - 辅助视频较短，进行循环处理...
2025-07-28 02:04:54 - VideoInterleaver - INFO - 视频循环处理使用: GPU加速
2025-07-28 02:04:57 - VideoInterleaver - INFO - 视频循环处理完成: C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\processed_aux.mp4
2025-07-28 02:04:57 - VideoInterleaver - INFO - 开始标准化视频参数...
2025-07-28 02:04:57 - VideoInterleaver - INFO - 获取视频 1 信息: C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4
2025-07-28 02:04:57 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4
2025-07-28 02:04:57 - VideoInterleaver - INFO - 视频信息 C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4: 648x1128 @ 30.00fps
2025-07-28 02:04:57 - VideoInterleaver - INFO - 获取视频 2 信息: C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\processed_aux.mp4
2025-07-28 02:04:57 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\processed_aux.mp4
2025-07-28 02:04:58 - VideoInterleaver - INFO - 视频信息 C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\processed_aux.mp4: 1080x1920 @ 25.00fps
2025-07-28 02:04:58 - VideoInterleaver - INFO - 分辨率优化: 648x1128 -> 1102x1920
2025-07-28 02:04:58 - VideoInterleaver - INFO - 主视频分辨率: 648x1128
2025-07-28 02:04:58 - VideoInterleaver - INFO - 目标分辨率: 1102x1920 @ 30.00fps
2025-07-28 02:04:58 - VideoInterleaver - INFO - 视频 1 需要转换参数
2025-07-28 02:04:58 - VideoInterleaver - INFO - 尝试使用NVENC GPU加速
2025-07-28 02:04:58 - VideoInterleaver - INFO - 执行NVENC命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4 -vf scale=1102:1920,fps=30.0,setsar=1 -c:v h264_nvenc -preset fast -b:v 10000k -maxrate 15000k -bufsize 20000k -c:a copy -y C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\normalized_0.mp4
2025-07-28 02:05:02 - VideoInterleaver - INFO - NVENC视频转换成功: C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\normalized_0.mp4 (大小: 86475234 字节)
2025-07-28 02:05:02 - VideoInterleaver - INFO - 视频 1 已标准化: C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\normalized_0.mp4
2025-07-28 02:05:02 - VideoInterleaver - INFO - 视频 2 需要转换参数
2025-07-28 02:05:02 - VideoInterleaver - INFO - 尝试使用NVENC GPU加速
2025-07-28 02:05:02 - VideoInterleaver - INFO - 执行NVENC命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\processed_aux.mp4 -vf scale=1102:1920,fps=30.0,setsar=1 -c:v h264_nvenc -preset fast -b:v 10000k -maxrate 15000k -bufsize 20000k -c:a copy -y C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\normalized_1.mp4
2025-07-28 02:05:06 - VideoInterleaver - INFO - NVENC视频转换成功: C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\normalized_1.mp4 (大小: 84462339 字节)
2025-07-28 02:05:06 - VideoInterleaver - INFO - 视频 2 已标准化: C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\normalized_1.mp4
2025-07-28 02:05:06 - VideoInterleaver - INFO - 所有视频标准化完成，共处理 2 个视频
2025-07-28 02:05:06 - VideoInterleaver - INFO - 构建FFmpeg命令...
2025-07-28 02:05:06 - VideoInterleaver - INFO - 使用交错模式: AB, 滤镜: [a][b]interleave=nb_inputs=2:duration=longest[out]
2025-07-28 02:05:06 - VideoInterleaver - INFO - 使用NVENC GPU加速编码
2025-07-28 02:05:06 - VideoInterleaver - INFO - 执行FFmpeg命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\normalized_0.mp4 -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\normalized_1.mp4 -filter_complex [0:v]fps=120,setsar=1[a];[1:v]fps=120,setsar=1[b];[a][b]interleave=nb_inputs=2:duration=longest[out] -map [out] -c:v h264_nvenc -preset fast -b:v 50000k -maxrate 50000k -bufsize 100000k -g 3 -r 120 -y C:/Users/<USER>/Desktop/1.mp4
2025-07-28 02:05:06 - VideoInterleaver - INFO - 开始视频交错处理...
2025-07-28 02:05:06 - VideoInterleaver - ERROR - 视频交错处理失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\normalized_0.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:01:06.30, start: 0.000000, bitrate: 10434 kb/s
  Stream #0:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(progressive), 1102x1920 [SAR 1:1 DAR 551:960], 10296 kb/s, 30 fps, 30 tbr, 15360 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 h264_nvenc
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 130 kb/s (default)
      Metadata:
        handler_name    : Core Media Audio
        vendor_id       : [0][0][0][0]
Input #1, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\normalized_1.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:01:06.33, start: 0.000000, bitrate: 10186 kb/s
  Stream #1:0[0x1](eng): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(progressive), 1102x1920 [SAR 1:1 DAR 551:960], 10049 kb/s, 30 fps, 30 tbr, 15360 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 h264_nvenc
  Stream #1:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
[AVFormatContext @ 000002aba3d743c0] Unable to choose an output format for 'pipe:1'; use a standard extension for the filename or specify the format manually.
[out#0 @ 000002aba3e7b3c0] Error initializing the muxer for pipe:1: Invalid argument
Error opening output file pipe:1.
Error opening output files: Invalid argument

2025-07-28 02:05:06 - VideoInterleaver - ERROR - 输出文件未生成
2025-07-28 02:06:25 - progress_test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:07:07 - VideoInterleaver - INFO - 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q
2025-07-28 02:07:07 - MainWindow - INFO - 主窗口关闭
2025-07-28 02:07:07 - MainWindow - INFO - 主窗口关闭
2025-07-28 02:07:07 - root - INFO - 应用程序退出，退出码: 0
2025-07-28 02:09:43 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:09:43 - root - INFO - 应用程序启动
2025-07-28 02:09:43 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:09:43 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:09:43 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 02:09:45 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 02:09:45 - VideoInterleaver - INFO - GPU加速已启用: NVIDIA GeForce GTX 1060 3GB: h264_nvenc, hevc_nvenc
2025-07-28 02:09:45 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 02:09:45 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 02:09:45 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 02:09:45 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:09:45 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:09:45 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:09:45 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:09:46 - root - INFO - 主窗口已显示
2025-07-28 02:10:23 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 02:15:30 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:15:30 - root - INFO - 应用程序启动
2025-07-28 02:15:30 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:15:30 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:15:30 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 02:15:31 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 02:15:31 - VideoInterleaver - INFO - 使用CPU编码
2025-07-28 02:15:31 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 02:15:31 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 02:15:31 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 02:15:31 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:15:31 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:15:31 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:15:31 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:15:32 - root - INFO - 主窗口已显示
2025-07-28 02:16:05 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 02:16:05 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 02:16:05 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/封面合成/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！_改善含_cover_20250727_202544_8c1c6360.mp4
2025-07-28 02:16:05 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_11.mp4
2025-07-28 02:16:05 - VideoInterleaver - INFO - 开始匹配视频时长...
2025-07-28 02:16:05 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！_改善含_cover_20250727_202544_8c1c6360.mp4
2025-07-28 02:16:05 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/辅助视频/time_11.mp4
2025-07-28 02:16:05 - VideoInterleaver - INFO - 主视频时长: 14.86秒
2025-07-28 02:16:05 - VideoInterleaver - INFO - 辅助视频时长: 7.42秒
2025-07-28 02:16:05 - VideoInterleaver - INFO - 辅助视频较短，进行循环处理...
2025-07-28 02:16:05 - VideoInterleaver - INFO - 视频循环处理使用: CPU编码
2025-07-28 02:16:10 - VideoInterleaver - INFO - 视频循环处理完成: C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\processed_aux.mp4
2025-07-28 02:16:10 - VideoInterleaver - INFO - 开始标准化视频参数...
2025-07-28 02:16:10 - VideoInterleaver - INFO - 获取视频 1 信息: C:/Users/<USER>/Desktop/封面合成/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！_改善含_cover_20250727_202544_8c1c6360.mp4
2025-07-28 02:16:10 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！_改善含_cover_20250727_202544_8c1c6360.mp4
2025-07-28 02:16:10 - VideoInterleaver - INFO - 视频信息 C:/Users/<USER>/Desktop/封面合成/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！_改善含_cover_20250727_202544_8c1c6360.mp4: 720x1280 @ 36.00fps
2025-07-28 02:16:10 - VideoInterleaver - INFO - 获取视频 2 信息: C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\processed_aux.mp4
2025-07-28 02:16:10 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\processed_aux.mp4
2025-07-28 02:16:10 - VideoInterleaver - INFO - 视频信息 C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\processed_aux.mp4: 1080x1920 @ 25.00fps
2025-07-28 02:16:10 - VideoInterleaver - INFO - 分辨率优化: 720x1280 -> 1080x1920
2025-07-28 02:16:10 - VideoInterleaver - INFO - 主视频分辨率: 720x1280
2025-07-28 02:16:10 - VideoInterleaver - INFO - 目标分辨率: 1080x1920 @ 36.00fps
2025-07-28 02:16:10 - VideoInterleaver - INFO - 视频 1 需要转换参数
2025-07-28 02:16:10 - VideoInterleaver - INFO - 使用CPU编码进行视频参数转换
2025-07-28 02:16:10 - VideoInterleaver - INFO - 执行CPU编码命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:/Users/<USER>/Desktop/封面合成/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！_改善含_cover_20250727_202544_8c1c6360.mp4 -vf scale=1080:1920,fps=36.0,setsar=1 -c:v libx264 -crf 18 -preset fast -c:a copy -y C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\normalized_0.mp4
2025-07-28 02:16:18 - VideoInterleaver - INFO - CPU视频转换成功: C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\normalized_0.mp4 (大小: 20794112 字节)
2025-07-28 02:16:18 - VideoInterleaver - INFO - 视频 1 已标准化: C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\normalized_0.mp4
2025-07-28 02:16:18 - VideoInterleaver - INFO - 视频 2 需要转换参数
2025-07-28 02:16:18 - VideoInterleaver - INFO - 使用CPU编码进行视频参数转换
2025-07-28 02:16:18 - VideoInterleaver - INFO - 执行CPU编码命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\processed_aux.mp4 -vf scale=1080:1920,fps=36.0,setsar=1 -c:v libx264 -crf 18 -preset fast -c:a copy -y C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\normalized_1.mp4
2025-07-28 02:16:25 - VideoInterleaver - INFO - CPU视频转换成功: C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\normalized_1.mp4 (大小: 16434611 字节)
2025-07-28 02:16:25 - VideoInterleaver - INFO - 视频 2 已标准化: C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\normalized_1.mp4
2025-07-28 02:16:25 - VideoInterleaver - INFO - 所有视频标准化完成，共处理 2 个视频
2025-07-28 02:16:25 - VideoInterleaver - INFO - 构建FFmpeg命令...
2025-07-28 02:16:25 - VideoInterleaver - INFO - 使用交错模式: AB, 滤镜: [a][b]interleave=nb_inputs=2:duration=longest[out]
2025-07-28 02:16:25 - VideoInterleaver - INFO - 使用CPU编码
2025-07-28 02:16:25 - VideoInterleaver - INFO - 执行FFmpeg命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\normalized_0.mp4 -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\normalized_1.mp4 -filter_complex [0:v]fps=120,setsar=1[a];[1:v]fps=120,setsar=1[b];[a][b]interleave=nb_inputs=2:duration=longest[out] -map [out] -c:v libx264 -preset medium -crf 20 -keyint_min 1 -g 3 -bf 2 -b_strategy 2 -b:v 50000k -r 120 -y C:/Users/<USER>/Desktop/1.mp4
2025-07-28 02:16:25 - VideoInterleaver - INFO - 开始视频交错处理...
2025-07-28 02:16:25 - VideoInterleaver - ERROR - 视频交错处理失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\normalized_0.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:00:14.86, start: 0.000000, bitrate: 11193 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 11057 kb/s, 36 fps, 36 tbr, 18432 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 127 kb/s (default)
      Metadata:
        handler_name    : Core Media Audio
        vendor_id       : [0][0][0][0]
Input #1, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\normalized_1.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:00:14.89, start: 0.000000, bitrate: 8830 kb/s
  Stream #1:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 8691 kb/s, 36 fps, 36 tbr, 18432 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #1:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
[out#0/mp4 @ 00000147321deec0] Error opening output C:/Users/<USER>/Desktop/1.mp4: Permission denied
Error opening output file C:/Users/<USER>/Desktop/1.mp4.
Error opening output files: Permission denied

2025-07-28 02:16:25 - VideoInterleaver - ERROR - 输出文件未生成
2025-07-28 02:19:07 - VideoInterleaver - INFO - 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn
2025-07-28 02:19:07 - MainWindow - INFO - 主窗口关闭
2025-07-28 02:19:07 - MainWindow - INFO - 主窗口关闭
2025-07-28 02:19:07 - root - INFO - 应用程序退出，退出码: 0
2025-07-28 02:19:35 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:19:35 - root - INFO - 应用程序启动
2025-07-28 02:19:35 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:19:35 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:19:35 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 02:19:37 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 02:19:37 - VideoInterleaver - INFO - 使用CPU编码
2025-07-28 02:19:37 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 02:19:37 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 02:19:37 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 02:19:37 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:19:37 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:19:37 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:19:37 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:19:38 - root - INFO - 主窗口已显示
2025-07-28 02:20:24 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 02:20:24 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 02:20:24 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/封面合成/大学生！别走！行李箱你就这么选，有毛病算我的！_行李箱-快手_cover_20250727_202529_b575a2af.mp4
2025-07-28 02:20:24 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_10.mp4
2025-07-28 02:20:24 - VideoInterleaver - INFO - 开始匹配视频时长...
2025-07-28 02:20:24 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/大学生！别走！行李箱你就这么选，有毛病算我的！_行李箱-快手_cover_20250727_202529_b575a2af.mp4
2025-07-28 02:20:24 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/辅助视频/time_10.mp4
2025-07-28 02:20:24 - VideoInterleaver - INFO - 主视频时长: 64.81秒
2025-07-28 02:20:24 - VideoInterleaver - INFO - 辅助视频时长: 7.62秒
2025-07-28 02:20:24 - VideoInterleaver - INFO - 辅助视频较短，进行循环处理...
2025-07-28 02:20:24 - VideoInterleaver - INFO - 视频循环处理使用: CPU编码
2025-07-28 02:20:47 - VideoInterleaver - INFO - 视频循环处理完成: C:\Users\<USER>\AppData\Local\Temp\video_interleave_ega1ojrw\processed_aux.mp4
2025-07-28 02:20:47 - VideoInterleaver - INFO - 开始标准化视频参数...
2025-07-28 02:21:18 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:21:18 - root - INFO - 应用程序启动
2025-07-28 02:21:18 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:21:18 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:21:18 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 02:21:20 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 02:21:20 - VideoInterleaver - INFO - 使用CPU编码
2025-07-28 02:21:20 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 02:21:20 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 02:21:20 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 02:21:20 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:21:20 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:21:20 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:21:20 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:21:21 - root - INFO - 主窗口已显示
2025-07-28 02:21:58 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 02:21:58 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 02:21:58 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/封面合成/缘品堂O3x4mgdnjkky2ct2_的精彩视频-快手_cover_20250727_202508_440a788f.mp4
2025-07-28 02:21:58 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_26.mp4
2025-07-28 02:21:58 - VideoInterleaver - INFO - 开始匹配视频时长...
2025-07-28 02:21:58 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/缘品堂O3x4mgdnjkky2ct2_的精彩视频-快手_cover_20250727_202508_440a788f.mp4
2025-07-28 02:21:58 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/辅助视频/time_26.mp4
2025-07-28 02:21:58 - VideoInterleaver - INFO - 主视频时长: 43.52秒
2025-07-28 02:21:58 - VideoInterleaver - INFO - 辅助视频时长: 8.02秒
2025-07-28 02:21:58 - VideoInterleaver - INFO - 辅助视频较短，进行循环处理...
2025-07-28 02:21:58 - VideoInterleaver - INFO - 视频循环处理使用: CPU编码
2025-07-28 02:22:14 - VideoInterleaver - INFO - 视频循环处理完成: C:\Users\<USER>\AppData\Local\Temp\video_interleave_kzmtbpz8\processed_aux.mp4
2025-07-28 02:22:14 - VideoInterleaver - INFO - 开始标准化视频参数...
2025-07-28 02:22:14 - VideoInterleaver - INFO - 获取视频 1 信息: C:/Users/<USER>/Desktop/封面合成/缘品堂O3x4mgdnjkky2ct2_的精彩视频-快手_cover_20250727_202508_440a788f.mp4
2025-07-28 02:22:14 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/封面合成/缘品堂O3x4mgdnjkky2ct2_的精彩视频-快手_cover_20250727_202508_440a788f.mp4
2025-07-28 02:22:15 - VideoInterleaver - INFO - 视频信息 C:/Users/<USER>/Desktop/封面合成/缘品堂O3x4mgdnjkky2ct2_的精彩视频-快手_cover_20250727_202508_440a788f.mp4: 720x1280 @ 22.00fps
2025-07-28 02:22:15 - VideoInterleaver - INFO - 获取视频 2 信息: C:\Users\<USER>\AppData\Local\Temp\video_interleave_kzmtbpz8\processed_aux.mp4
2025-07-28 02:22:15 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:\Users\<USER>\AppData\Local\Temp\video_interleave_kzmtbpz8\processed_aux.mp4
2025-07-28 02:22:15 - VideoInterleaver - INFO - 视频信息 C:\Users\<USER>\AppData\Local\Temp\video_interleave_kzmtbpz8\processed_aux.mp4: 1080x1920 @ 25.00fps
2025-07-28 02:22:15 - VideoInterleaver - INFO - 分辨率优化: 720x1280 -> 1080x1920
2025-07-28 02:22:15 - VideoInterleaver - INFO - 主视频分辨率: 720x1280
2025-07-28 02:22:15 - VideoInterleaver - INFO - 目标分辨率: 1080x1920 @ 22.00fps
2025-07-28 02:22:15 - VideoInterleaver - INFO - 视频 1 需要转换参数
2025-07-28 02:22:15 - VideoInterleaver - INFO - 使用CPU编码进行视频参数转换
2025-07-28 02:22:15 - VideoInterleaver - INFO - 执行CPU编码命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:/Users/<USER>/Desktop/封面合成/缘品堂O3x4mgdnjkky2ct2_的精彩视频-快手_cover_20250727_202508_440a788f.mp4 -vf scale=1080:1920,fps=22.0,setsar=1 -c:v libx264 -crf 18 -preset fast -c:a copy -y C:\Users\<USER>\AppData\Local\Temp\video_interleave_kzmtbpz8\normalized_0.mp4
2025-07-28 02:22:28 - VideoInterleaver - INFO - CPU视频转换成功: C:\Users\<USER>\AppData\Local\Temp\video_interleave_kzmtbpz8\normalized_0.mp4 (大小: 36602675 字节)
2025-07-28 02:22:28 - VideoInterleaver - INFO - 视频 1 已标准化: C:\Users\<USER>\AppData\Local\Temp\video_interleave_kzmtbpz8\normalized_0.mp4
2025-07-28 02:22:28 - VideoInterleaver - INFO - 视频 2 需要转换参数
2025-07-28 02:22:28 - VideoInterleaver - INFO - 使用CPU编码进行视频参数转换
2025-07-28 02:22:28 - VideoInterleaver - INFO - 执行CPU编码命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_kzmtbpz8\processed_aux.mp4 -vf scale=1080:1920,fps=22.0,setsar=1 -c:v libx264 -crf 18 -preset fast -c:a copy -y C:\Users\<USER>\AppData\Local\Temp\video_interleave_kzmtbpz8\normalized_1.mp4
2025-07-28 02:22:43 - VideoInterleaver - INFO - CPU视频转换成功: C:\Users\<USER>\AppData\Local\Temp\video_interleave_kzmtbpz8\normalized_1.mp4 (大小: 43472819 字节)
2025-07-28 02:22:43 - VideoInterleaver - INFO - 视频 2 已标准化: C:\Users\<USER>\AppData\Local\Temp\video_interleave_kzmtbpz8\normalized_1.mp4
2025-07-28 02:22:43 - VideoInterleaver - INFO - 所有视频标准化完成，共处理 2 个视频
2025-07-28 02:22:43 - VideoInterleaver - INFO - 构建FFmpeg命令...
2025-07-28 02:22:43 - VideoInterleaver - INFO - 使用交错模式: AB, 滤镜: [a][b]interleave=nb_inputs=2:duration=longest[out]
2025-07-28 02:22:43 - VideoInterleaver - INFO - 使用CPU编码
2025-07-28 02:22:43 - VideoInterleaver - INFO - 执行FFmpeg命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_kzmtbpz8\normalized_0.mp4 -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_kzmtbpz8\normalized_1.mp4 -filter_complex [0:v]fps=120,setsar=1[a];[1:v]fps=120,setsar=1[b];[a][b]interleave=nb_inputs=2:duration=longest[out] -map [out] -c:v libx264 -preset medium -crf 20 -keyint_min 1 -g 3 -bf 2 -b_strategy 2 -b:v 50000k -r 120 -y C:/Users/<USER>/Desktop/1.mp4
2025-07-28 02:22:43 - VideoInterleaver - INFO - 开始视频交错处理...
2025-07-28 02:22:44 - VideoInterleaver - INFO - 视频交错处理进行中...
2025-07-28 02:22:47 - VideoInterleaver - INFO - 视频交错处理进行中...
2025-07-28 02:22:51 - VideoInterleaver - INFO - 视频交错处理进行中...
2025-07-28 02:22:54 - VideoInterleaver - INFO - 视频交错处理进行中...
2025-07-28 02:22:57 - VideoInterleaver - INFO - 视频交错处理进行中...
2025-07-28 02:23:00 - VideoInterleaver - INFO - 视频交错处理进行中...
2025-07-28 02:23:03 - VideoInterleaver - INFO - 视频交错处理进行中...
2025-07-28 02:23:06 - VideoInterleaver - INFO - 视频交错处理进行中...
2025-07-28 02:23:09 - VideoInterleaver - INFO - 视频交错处理进行中...
2025-07-28 02:23:12 - VideoInterleaver - INFO - 视频交错处理进行中...
2025-07-28 02:23:15 - VideoInterleaver - INFO - 视频交错处理进行中...
2025-07-28 02:23:16 - VideoInterleaver - INFO - 视频交错处理完成
2025-07-28 02:23:16 - VideoInterleaver - INFO - 视频交错完成: C:/Users/<USER>/Desktop/1.mp4 (大小: 157475123 字节)
2025-07-28 02:27:35 - interleave_test - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:32:44 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:32:44 - root - INFO - 应用程序启动
2025-07-28 02:32:44 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:32:44 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:32:45 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 02:32:46 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 02:32:46 - VideoInterleaver - INFO - 使用CPU编码
2025-07-28 02:32:46 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 02:32:46 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 02:32:46 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 02:32:46 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:32:46 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:32:46 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:32:46 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:32:47 - root - INFO - 主窗口已显示
2025-07-28 02:33:40 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 02:33:40 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 02:33:40 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/封面合成/大学生！别走！行李箱你就这么选，有毛病算我的！_行李箱-快手_cover_20250727_202529_b575a2af.mp4
2025-07-28 02:33:40 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_9.mp4
2025-07-28 02:38:04 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:38:04 - root - INFO - 应用程序启动
2025-07-28 02:38:04 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:38:04 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:38:04 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 02:38:06 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 02:38:06 - VideoInterleaver - INFO - 使用CPU编码
2025-07-28 02:38:06 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 02:38:06 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 02:38:06 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 02:38:06 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:38:06 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:38:06 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:38:06 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:38:07 - root - INFO - 主窗口已显示
2025-07-28 02:39:27 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 02:39:27 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 02:39:27 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 02:39:27 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_27.mp4
2025-07-28 02:39:27 - VideoInterleaver - INFO - 开始匹配视频时长...
2025-07-28 02:39:27 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 02:39:27 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/辅助视频/time_27.mp4
2025-07-28 02:39:27 - VideoInterleaver - INFO - 主视频时长: 15.39秒
2025-07-28 02:39:27 - VideoInterleaver - INFO - 辅助视频时长: 7.10秒
2025-07-28 02:39:27 - VideoInterleaver - INFO - 辅助视频较短，进行循环处理...
2025-07-28 02:39:27 - VideoInterleaver - INFO - 视频循环处理使用: CPU编码
2025-07-28 02:39:32 - VideoInterleaver - INFO - 视频循环处理完成: C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0\processed_aux.mp4
2025-07-28 02:39:32 - VideoInterleaver - INFO - 开始标准化视频参数...
2025-07-28 02:39:32 - VideoInterleaver - INFO - 获取视频 1 信息: C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 02:39:32 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 02:39:32 - VideoInterleaver - INFO - 视频信息 C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4: 720x1280 @ 36.00fps
2025-07-28 02:39:32 - VideoInterleaver - INFO - 获取视频 2 信息: C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0\processed_aux.mp4
2025-07-28 02:39:32 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0\processed_aux.mp4
2025-07-28 02:39:32 - VideoInterleaver - INFO - 视频信息 C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0\processed_aux.mp4: 1080x1920 @ 25.00fps
2025-07-28 02:39:32 - VideoInterleaver - INFO - 分辨率优化: 720x1280 -> 1080x1920
2025-07-28 02:39:32 - VideoInterleaver - INFO - 主视频分辨率: 720x1280
2025-07-28 02:39:32 - VideoInterleaver - INFO - 目标分辨率: 1080x1920 @ 36.00fps
2025-07-28 02:39:32 - VideoInterleaver - INFO - 视频 1 需要转换参数
2025-07-28 02:39:32 - VideoInterleaver - INFO - 使用CPU编码进行视频参数转换
2025-07-28 02:39:32 - VideoInterleaver - INFO - 执行CPU编码命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4 -vf scale=1080:1920,fps=36.0,setsar=1 -c:v libx264 -crf 18 -preset fast -c:a copy -y C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0\normalized_0.mp4
2025-07-28 02:39:40 - VideoInterleaver - INFO - CPU视频转换成功: C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0\normalized_0.mp4 (大小: 20044635 字节)
2025-07-28 02:39:40 - VideoInterleaver - INFO - 视频 1 已标准化: C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0\normalized_0.mp4
2025-07-28 02:39:40 - VideoInterleaver - INFO - 视频 2 需要转换参数
2025-07-28 02:39:40 - VideoInterleaver - INFO - 使用CPU编码进行视频参数转换
2025-07-28 02:39:40 - VideoInterleaver - INFO - 执行CPU编码命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0\processed_aux.mp4 -vf scale=1080:1920,fps=36.0,setsar=1 -c:v libx264 -crf 18 -preset fast -c:a copy -y C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0\normalized_1.mp4
2025-07-28 02:39:46 - VideoInterleaver - INFO - CPU视频转换成功: C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0\normalized_1.mp4 (大小: 15209794 字节)
2025-07-28 02:39:46 - VideoInterleaver - INFO - 视频 2 已标准化: C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0\normalized_1.mp4
2025-07-28 02:39:46 - VideoInterleaver - INFO - 所有视频标准化完成，共处理 2 个视频
2025-07-28 02:39:46 - VideoInterleaver - INFO - 构建FFmpeg命令...
2025-07-28 02:39:46 - VideoInterleaver - INFO - 使用交错模式: AB, 滤镜: [a][b_loop]interleave=nb_inputs=2[out]
2025-07-28 02:39:46 - VideoInterleaver - INFO - 使用CPU编码
2025-07-28 02:39:46 - VideoInterleaver - INFO - 执行FFmpeg命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0\normalized_0.mp4 -i C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0\normalized_1.mp4 -filter_complex [0:v]fps=120,setsar=1[a];[1:v]fps=120,setsar=1[b];[b]loop=loop=10:size=1000[b_loop];[a][b_loop]interleave=nb_inputs=2[out] -map [out] -c:v libx264 -preset medium -crf 20 -keyint_min 1 -g 3 -bf 2 -b_strategy 2 -b:v 50000k -r 120 -y C:/Users/<USER>/Desktop/3.mp4
2025-07-28 02:39:46 - VideoInterleaver - INFO - 开始视频交错处理...
2025-07-28 02:39:47 - VideoInterleaver - INFO - 视频交错处理进行中... (1秒)
2025-07-28 02:39:53 - VideoInterleaver - INFO - 视频交错处理进行中... (6秒)
2025-07-28 02:39:58 - VideoInterleaver - INFO - 视频交错处理进行中... (11秒)
2025-07-28 02:40:03 - VideoInterleaver - INFO - 视频交错处理进行中... (16秒)
2025-07-28 02:40:08 - VideoInterleaver - INFO - 视频交错处理进行中... (21秒)
2025-07-28 02:40:13 - VideoInterleaver - INFO - 视频交错处理进行中... (26秒)
2025-07-28 02:40:18 - VideoInterleaver - INFO - 视频交错处理进行中... (32秒)
2025-07-28 02:40:24 - VideoInterleaver - INFO - 视频交错处理进行中... (37秒)
2025-07-28 02:40:29 - VideoInterleaver - INFO - 视频交错处理进行中... (42秒)
2025-07-28 02:40:34 - VideoInterleaver - INFO - 视频交错处理进行中... (47秒)
2025-07-28 02:40:39 - VideoInterleaver - INFO - 视频交错处理进行中... (52秒)
2025-07-28 02:40:44 - VideoInterleaver - INFO - 视频交错处理进行中... (58秒)
2025-07-28 02:40:49 - VideoInterleaver - INFO - 视频交错处理进行中... (63秒)
2025-07-28 02:40:55 - VideoInterleaver - INFO - 视频交错处理进行中... (68秒)
2025-07-28 02:41:00 - VideoInterleaver - INFO - 视频交错处理进行中... (73秒)
2025-07-28 02:41:05 - VideoInterleaver - INFO - 视频交错处理进行中... (78秒)
2025-07-28 02:41:10 - VideoInterleaver - INFO - 视频交错处理进行中... (83秒)
2025-07-28 02:41:15 - VideoInterleaver - INFO - 视频交错处理进行中... (89秒)
2025-07-28 02:41:20 - VideoInterleaver - INFO - 视频交错处理进行中... (94秒)
2025-07-28 02:41:26 - VideoInterleaver - INFO - 视频交错处理进行中... (99秒)
2025-07-28 02:41:31 - VideoInterleaver - INFO - 视频交错处理进行中... (104秒)
2025-07-28 02:41:36 - VideoInterleaver - INFO - 视频交错处理进行中... (109秒)
2025-07-28 02:41:41 - VideoInterleaver - INFO - 视频交错处理进行中... (114秒)
2025-07-28 02:41:46 - VideoInterleaver - INFO - 视频交错处理进行中... (120秒)
2025-07-28 02:41:52 - VideoInterleaver - INFO - 视频交错处理进行中... (125秒)
2025-07-28 02:41:57 - VideoInterleaver - INFO - 视频交错处理进行中... (130秒)
2025-07-28 02:42:02 - VideoInterleaver - INFO - 视频交错处理进行中... (135秒)
2025-07-28 02:42:07 - VideoInterleaver - INFO - 视频交错处理进行中... (140秒)
2025-07-28 02:42:12 - VideoInterleaver - INFO - 视频交错处理进行中... (146秒)
2025-07-28 02:42:17 - VideoInterleaver - INFO - 视频交错处理进行中... (151秒)
2025-07-28 02:42:23 - VideoInterleaver - INFO - 视频交错处理进行中... (156秒)
2025-07-28 02:42:28 - VideoInterleaver - INFO - 视频交错处理进行中... (161秒)
2025-07-28 02:42:33 - VideoInterleaver - INFO - 视频交错处理进行中... (166秒)
2025-07-28 02:42:38 - VideoInterleaver - INFO - 视频交错处理进行中... (171秒)
2025-07-28 02:42:41 - VideoInterleaver - INFO - 视频交错处理完成
2025-07-28 02:42:41 - VideoInterleaver - INFO - 视频交错完成: C:/Users/<USER>/Desktop/3.mp4 (大小: 359135201 字节)
2025-07-28 02:48:36 - VideoInterleaver - INFO - 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\video_interleave_r1hr9rh0
2025-07-28 02:48:36 - MainWindow - INFO - 主窗口关闭
2025-07-28 02:48:36 - MainWindow - INFO - 主窗口关闭
2025-07-28 02:48:36 - root - INFO - 应用程序退出，退出码: 0
2025-07-28 02:56:20 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:56:20 - root - INFO - 应用程序启动
2025-07-28 02:56:20 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:56:20 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:56:20 - root - ERROR - 应用程序运行时发生错误: name 'QApplication' is not defined
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\main.py", line 115, in main
    main_window = MainWindow()
                  ^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\gui\main_window.py", line 99, in __init__
    screen = QApplication.primaryScreen().geometry()
             ^^^^^^^^^^^^
NameError: name 'QApplication' is not defined
2025-07-28 02:58:35 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:58:35 - root - INFO - 应用程序启动
2025-07-28 02:58:35 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:58:35 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 02:58:35 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:58:35 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 02:58:35 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 02:58:37 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 02:58:37 - VideoInterleaver - INFO - GPU加速已启用: NVIDIA GeForce GTX 1060 3GB: h264_nvenc, hevc_nvenc
2025-07-28 02:58:37 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 02:58:37 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 02:58:37 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 02:58:37 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:58:37 - MainWindow - INFO - 组件初始化完成
2025-07-28 02:58:38 - root - INFO - 主窗口已显示
2025-07-28 02:59:08 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 02:59:08 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 02:59:08 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 02:59:08 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_10.mp4
2025-07-28 02:59:08 - VideoInterleaver - INFO - 开始匹配视频时长...
2025-07-28 02:59:08 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 03:02:03 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:02:03 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 03:02:05 - MainWindow - INFO - 组件初始化完成
2025-07-28 03:03:46 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:03:46 - root - INFO - 应用程序启动
2025-07-28 03:03:46 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:03:46 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:03:46 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 03:03:46 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 03:03:46 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 03:03:47 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 03:03:47 - VideoInterleaver - INFO - 使用CPU编码
2025-07-28 03:03:47 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 03:03:47 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 03:03:47 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 03:03:47 - MainWindow - INFO - 组件初始化完成
2025-07-28 03:03:47 - MainWindow - INFO - 组件初始化完成
2025-07-28 03:03:48 - root - INFO - 主窗口已显示
2025-07-28 03:04:31 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 03:04:31 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 03:04:31 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 03:04:31 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_19.mp4
2025-07-28 03:04:31 - VideoInterleaver - INFO - 开始匹配视频时长...
2025-07-28 03:08:05 - root - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:08:05 - root - INFO - 应用程序启动
2025-07-28 03:08:05 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:08:05 - MainWindow - INFO - 日志系统初始化完成，日志文件: c:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:08:05 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 03:08:05 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 03:08:05 - VideoInterleaver - INFO - 配置文件加载成功: c:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 03:08:07 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 03:08:07 - VideoInterleaver - INFO - 使用CPU编码
2025-07-28 03:08:07 - VideoInterleaver - INFO - FFmpeg 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 03:08:07 - VideoInterleaver - INFO - FFprobe 可用: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 03:08:07 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 03:08:07 - MainWindow - INFO - 组件初始化完成
2025-07-28 03:08:07 - MainWindow - INFO - 组件初始化完成
2025-07-28 03:08:08 - root - INFO - 主窗口已显示
2025-07-28 03:08:36 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 03:08:36 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 03:08:36 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 03:08:36 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_2.mp4
2025-07-28 03:08:36 - VideoInterleaver - INFO - 开始匹配视频时长...
2025-07-28 03:08:36 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 03:08:36 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/辅助视频/time_2.mp4
2025-07-28 03:08:36 - VideoInterleaver - INFO - 主视频时长: 15.39秒
2025-07-28 03:08:36 - VideoInterleaver - INFO - 辅助视频时长: 8.02秒
2025-07-28 03:08:36 - VideoInterleaver - INFO - 辅助视频较短，进行循环处理...
2025-07-28 03:08:36 - VideoInterleaver - INFO - 视频循环处理使用: CPU编码
2025-07-28 03:08:42 - VideoInterleaver - INFO - 视频循环处理完成: C:\Users\<USER>\AppData\Local\Temp\video_interleave_wrymu1a2\processed_aux.mp4
2025-07-28 03:08:42 - VideoInterleaver - INFO - 开始标准化视频参数...
2025-07-28 03:08:42 - VideoInterleaver - INFO - 获取视频 1 信息: C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 03:08:42 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 03:08:42 - VideoInterleaver - INFO - 视频信息 C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4: 720x1280 @ 36.00fps
2025-07-28 03:08:42 - VideoInterleaver - INFO - 获取视频 2 信息: C:\Users\<USER>\AppData\Local\Temp\video_interleave_wrymu1a2\processed_aux.mp4
2025-07-28 03:08:42 - VideoInterleaver - INFO - 执行FFprobe命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:\Users\<USER>\AppData\Local\Temp\video_interleave_wrymu1a2\processed_aux.mp4
2025-07-28 03:08:42 - VideoInterleaver - INFO - 视频信息 C:\Users\<USER>\AppData\Local\Temp\video_interleave_wrymu1a2\processed_aux.mp4: 1080x1920 @ 25.00fps
2025-07-28 03:08:42 - VideoInterleaver - INFO - 分辨率优化: 720x1280 -> 1080x1920
2025-07-28 03:08:42 - VideoInterleaver - INFO - 主视频分辨率: 720x1280
2025-07-28 03:08:42 - VideoInterleaver - INFO - 目标分辨率: 1080x1920 @ 36.00fps
2025-07-28 03:08:42 - VideoInterleaver - INFO - 视频 1 需要转换参数
2025-07-28 03:08:42 - VideoInterleaver - INFO - 使用CPU编码进行视频参数转换
2025-07-28 03:08:42 - VideoInterleaver - INFO - 执行CPU编码命令: c:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe -i C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4 -vf scale=1080:1920,fps=36.0,setsar=1 -c:v libx264 -crf 18 -preset fast -c:a copy -y C:\Users\<USER>\AppData\Local\Temp\video_interleave_wrymu1a2\normalized_0.mp4
2025-07-28 03:28:27 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:28:27 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 03:28:29 - MainWindow - INFO - 组件初始化完成
2025-07-28 03:34:56 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:34:56 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 03:34:58 - MainWindow - INFO - 组件初始化完成
2025-07-28 03:39:38 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:39:38 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 03:39:40 - MainWindow - INFO - 组件初始化完成
2025-07-28 03:47:09 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:47:09 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 03:47:10 - MainWindow - INFO - 组件初始化完成
2025-07-28 03:51:14 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:51:14 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 03:51:15 - MainWindow - INFO - 组件初始化完成
2025-07-28 03:52:49 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:52:49 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 03:52:51 - MainWindow - INFO - 组件初始化完成
2025-07-28 03:52:53 - MainWindow - INFO - 主窗口关闭
2025-07-28 03:59:24 - root - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:59:24 - root - INFO - 应用程序启动
2025-07-28 03:59:24 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:59:24 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 03:59:24 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 03:59:24 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 03:59:24 - VideoInterleaver - INFO - 配置文件加载成功: C:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 03:59:26 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 03:59:26 - VideoInterleaver - INFO - GPU加速已启用: NVIDIA GeForce GTX 1060 3GB: h264_nvenc, hevc_nvenc
2025-07-28 03:59:26 - VideoInterleaver - INFO - 使用修复后的NVENC参数，参考video_interleave_hiding.py实现
2025-07-28 03:59:26 - VideoInterleaver - INFO - FFmpeg 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 03:59:26 - VideoInterleaver - INFO - FFprobe 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 03:59:26 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 03:59:26 - MainWindow - INFO - 组件初始化完成
2025-07-28 03:59:26 - MainWindow - INFO - 组件初始化完成
2025-07-28 03:59:27 - root - INFO - 主窗口已显示
2025-07-28 03:59:44 - VideoInterleaver - INFO - 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\video_interleave__7kc78wn
2025-07-28 03:59:44 - MainWindow - INFO - 主窗口关闭
2025-07-28 03:59:44 - MainWindow - INFO - 主窗口关闭
2025-07-28 03:59:44 - root - INFO - 应用程序退出，退出码: 0
2025-07-28 04:04:33 - root - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 04:04:33 - root - INFO - 测试程序启动
2025-07-28 04:04:33 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 04:04:33 - MainWindow - INFO - 日志系统初始化完成，日志文件: C:\Users\<USER>\Desktop\7\video_interleave_project\logs\video_interleave_20250728.log
2025-07-28 04:04:33 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 04:04:33 - MainWindow - INFO - 主窗口初始化完成
2025-07-28 04:04:33 - VideoInterleaver - INFO - 配置文件加载成功: C:\Users\<USER>\Desktop\7\video_interleave_project\config.json
2025-07-28 04:04:35 - GPUAccelerator - INFO - 检测到GPU: ['nvidia']
2025-07-28 04:04:35 - VideoInterleaver - INFO - GPU加速已启用: NVIDIA GeForce GTX 1060 3GB: h264_nvenc, hevc_nvenc
2025-07-28 04:04:35 - VideoInterleaver - INFO - 使用修复后的NVENC参数，参考video_interleave_hiding.py实现
2025-07-28 04:04:35 - VideoInterleaver - INFO - FFmpeg 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffmpeg.exe
2025-07-28 04:04:35 - VideoInterleaver - INFO - FFprobe 可用: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe
2025-07-28 04:04:35 - VideoInterleaver - INFO - VideoInterleaver 初始化完成
2025-07-28 04:04:35 - MainWindow - INFO - 组件初始化完成
2025-07-28 04:04:35 - MainWindow - INFO - 组件初始化完成
2025-07-28 04:05:18 - VideoInterleaver - INFO - 开始交错处理: 主视频 + 辅助视频
2025-07-28 04:05:18 - VideoInterleaver - INFO - 使用预设: hanxing_ab
2025-07-28 04:05:18 - VideoInterleaver - INFO - 主视频: C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 04:05:18 - VideoInterleaver - INFO - 辅助视频: C:/Users/<USER>/Desktop/辅助视频/time_3.mp4
2025-07-28 04:05:18 - VideoInterleaver - INFO - 开始匹配视频时长...
2025-07-28 04:05:18 - VideoInterleaver - INFO - 执行FFprobe命令: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 04:05:18 - VideoInterleaver - INFO - 执行FFprobe命令: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/辅助视频/time_3.mp4
2025-07-28 04:05:18 - VideoInterleaver - INFO - 主视频时长: 15.39秒
2025-07-28 04:05:18 - VideoInterleaver - INFO - 辅助视频时长: 7.83秒
2025-07-28 04:05:18 - VideoInterleaver - INFO - 辅助视频较短，进行循环处理...
2025-07-28 04:05:18 - VideoInterleaver - INFO - 视频循环处理使用: GPU加速
2025-07-28 04:05:21 - VideoInterleaver - INFO - 视频循环处理完成: C:\Users\<USER>\AppData\Local\Temp\video_interleave_14c16o7p\processed_aux.mp4
2025-07-28 04:05:21 - VideoInterleaver - INFO - 开始标准化视频参数...
2025-07-28 04:05:21 - VideoInterleaver - INFO - 获取视频 1 信息: C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
2025-07-28 04:05:21 - VideoInterleaver - INFO - 执行FFprobe命令: C:\Users\<USER>\Desktop\7\video_interleave_project\bin\ffprobe.exe -v quiet -print_format json -show_format -show_streams C:/Users/<USER>/Desktop/Video/如果你家孩子经常含胸驼背，一定要及时纠正，这个背背佳穿戴方便，有助于孩子开肩美背，挺拔身姿！#改善含胸驼背 #矫正驼背神器推荐 #背背佳矫正带-快手.mp4
