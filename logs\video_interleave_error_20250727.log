2025-07-27 23:07:51 - root - ERROR - 应用程序运行时发生错误: type object 'ApplicationAttribute' has no attribute 'AA_EnableHighDpiScaling'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\main.py", line 105, in main
    app = setup_application()
          ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\main.py", line 29, in setup_application
    app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'ApplicationAttribute' has no attribute 'AA_EnableHighDpiScaling'
2025-07-27 23:15:12 - VideoInterleaver - ERROR - 获取视频信息失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-27 23:15:12 - VideoInterleaver - ERROR - 视频交错失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-27 23:15:12 - VideoInterleaver - ERROR - 错误详情: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 260, in interleave_videos
    normalized_videos = self.normalize_videos(all_videos)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 166, in normalize_videos
    info = self.get_video_info(path)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 125, in get_video_info
    info = json.loads(result.stdout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\__init__.py", line 339, in loads
    raise TypeError(f'the JSON object must be str, bytes or bytearray, '
TypeError: the JSON object must be str, bytes or bytearray, not NoneType

