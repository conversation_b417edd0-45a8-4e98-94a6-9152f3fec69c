2025-07-28 01:36:54 - VideoInterleaver - ERROR - 视频参数转换失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:04:19.68, start: 0.000000, bitrate: 6280 kb/s
  Stream #0:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 6145 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (h264_nvenc))
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
[h264_nvenc @ 000001ccb9185c40] [Eval @ 000000082e5fea20] Undefined constant or missing '(' in 'cq'
[h264_nvenc @ 000001ccb9185c40] Unable to parse option value "cq"
[h264_nvenc @ 000001ccb9185c40] Error setting option rc to value cq.
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Error while opening encoder - maybe incorrect parameters such as bit_rate, rate, width or height.
[vf#0:0 @ 000001ccb9a60ac0] Error sending frames to consumers: Invalid argument
[vf#0:0 @ 000001ccb9a60ac0] Task finished with error code: -22 (Invalid argument)
[vf#0:0 @ 000001ccb9a60ac0] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 000001ccb8c9a700] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!

2025-07-28 01:36:54 - VideoInterleaver - ERROR - FFmpeg返回码: 4294967274
2025-07-28 01:36:54 - VideoInterleaver - ERROR - 标准化视频 2 失败: FFmpeg执行失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:04:19.68, start: 0.000000, bitrate: 6280 kb/s
  Stream #0:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 6145 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (h264_nvenc))
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
[h264_nvenc @ 000001ccb9185c40] [Eval @ 000000082e5fea20] Undefined constant or missing '(' in 'cq'
[h264_nvenc @ 000001ccb9185c40] Unable to parse option value "cq"
[h264_nvenc @ 000001ccb9185c40] Error setting option rc to value cq.
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Error while opening encoder - maybe incorrect parameters such as bit_rate, rate, width or height.
[vf#0:0 @ 000001ccb9a60ac0] Error sending frames to consumers: Invalid argument
[vf#0:0 @ 000001ccb9a60ac0] Task finished with error code: -22 (Invalid argument)
[vf#0:0 @ 000001ccb9a60ac0] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 000001ccb8c9a700] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!

2025-07-28 01:36:54 - VideoInterleaver - ERROR - 视频标准化过程失败: 标准化视频 2 (C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4) 失败: FFmpeg执行失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:04:19.68, start: 0.000000, bitrate: 6280 kb/s
  Stream #0:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 6145 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (h264_nvenc))
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
[h264_nvenc @ 000001ccb9185c40] [Eval @ 000000082e5fea20] Undefined constant or missing '(' in 'cq'
[h264_nvenc @ 000001ccb9185c40] Unable to parse option value "cq"
[h264_nvenc @ 000001ccb9185c40] Error setting option rc to value cq.
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Error while opening encoder - maybe incorrect parameters such as bit_rate, rate, width or height.
[vf#0:0 @ 000001ccb9a60ac0] Error sending frames to consumers: Invalid argument
[vf#0:0 @ 000001ccb9a60ac0] Task finished with error code: -22 (Invalid argument)
[vf#0:0 @ 000001ccb9a60ac0] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 000001ccb8c9a700] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!

2025-07-28 01:36:54 - VideoInterleaver - ERROR - 视频交错失败: 标准化视频 2 (C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4) 失败: FFmpeg执行失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:04:19.68, start: 0.000000, bitrate: 6280 kb/s
  Stream #0:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 6145 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (h264_nvenc))
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
[h264_nvenc @ 000001ccb9185c40] [Eval @ 000000082e5fea20] Undefined constant or missing '(' in 'cq'
[h264_nvenc @ 000001ccb9185c40] Unable to parse option value "cq"
[h264_nvenc @ 000001ccb9185c40] Error setting option rc to value cq.
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Error while opening encoder - maybe incorrect parameters such as bit_rate, rate, width or height.
[vf#0:0 @ 000001ccb9a60ac0] Error sending frames to consumers: Invalid argument
[vf#0:0 @ 000001ccb9a60ac0] Task finished with error code: -22 (Invalid argument)
[vf#0:0 @ 000001ccb9a60ac0] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 000001ccb8c9a700] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!

2025-07-28 01:36:54 - VideoInterleaver - ERROR - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 449, in _convert_video_params
    result = subprocess.run(cmd, capture_output=True, text=True,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['c:\\Users\\<USER>\\Desktop\\7\\video_interleave_project\\bin\\ffmpeg.exe', '-i', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_interleave_ox8srg50\\processed_aux.mp4', '-vf', 'scale=720:1280,fps=30.0', '-c:v', 'h264_nvenc', '-preset', 'p4', '-cq', '20', '-rc', 'cq', '-spatial_aq', '1', '-temporal_aq', '1', '-gpu', '0', '-c:a', 'copy', '-y', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_interleave_ox8srg50\\normalized_1.mp4']' returned non-zero exit status 4294967274.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 267, in normalize_videos
    self._convert_video_params(path, output_path, target_width, target_height, target_fps)
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 465, in _convert_video_params
    raise RuntimeError(f"FFmpeg执行失败: {e.stderr}")
RuntimeError: FFmpeg执行失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:04:19.68, start: 0.000000, bitrate: 6280 kb/s
  Stream #0:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 6145 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (h264_nvenc))
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
[h264_nvenc @ 000001ccb9185c40] [Eval @ 000000082e5fea20] Undefined constant or missing '(' in 'cq'
[h264_nvenc @ 000001ccb9185c40] Unable to parse option value "cq"
[h264_nvenc @ 000001ccb9185c40] Error setting option rc to value cq.
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Error while opening encoder - maybe incorrect parameters such as bit_rate, rate, width or height.
[vf#0:0 @ 000001ccb9a60ac0] Error sending frames to consumers: Invalid argument
[vf#0:0 @ 000001ccb9a60ac0] Task finished with error code: -22 (Invalid argument)
[vf#0:0 @ 000001ccb9a60ac0] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 000001ccb8c9a700] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 517, in interleave_videos
    normalized_videos = self.normalize_videos(all_videos)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\core\interleaver.py", line 273, in normalize_videos
    raise RuntimeError(f"标准化视频 {i+1} ({path}) 失败: {e}")
RuntimeError: 标准化视频 2 (C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4) 失败: FFmpeg执行失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ox8srg50\processed_aux.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:04:19.68, start: 0.000000, bitrate: 6280 kb/s
  Stream #0:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 6145 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (h264_nvenc))
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
[h264_nvenc @ 000001ccb9185c40] [Eval @ 000000082e5fea20] Undefined constant or missing '(' in 'cq'
[h264_nvenc @ 000001ccb9185c40] Unable to parse option value "cq"
[h264_nvenc @ 000001ccb9185c40] Error setting option rc to value cq.
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Error while opening encoder - maybe incorrect parameters such as bit_rate, rate, width or height.
[vf#0:0 @ 000001ccb9a60ac0] Error sending frames to consumers: Invalid argument
[vf#0:0 @ 000001ccb9a60ac0] Task finished with error code: -22 (Invalid argument)
[vf#0:0 @ 000001ccb9a60ac0] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 000001ccb9a60040] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 000001ccb8c9a700] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!


2025-07-28 01:45:07 - VideoInterleaver - ERROR - FFmpeg执行失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:/Users/<USER>/Desktop/封面合成/有了这个__可视掏耳勺_就不用担心掏不干净了，脑洞大开了呀解锁很多新场景_好物分享__挖耳勺__解压_cover_20250727_202552_7622cc73.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:01:06.29, start: 0.076009, bitrate: 14380 kb/s
  Stream #0:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(progressive), 648x1128 [SAR 1:1 DAR 27:47], 14245 kb/s, 30 fps, 30 tbr, 15360 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 h264_nvenc
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 130 kb/s (default)
      Metadata:
        handler_name    : Core Media Audio
        vendor_id       : [0][0][0][0]
Input #1, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave__lstekol\normalized_1.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:01:06.33, start: 0.000000, bitrate: 10169 kb/s
  Stream #1:0[0x1](eng): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(progressive), 648x1128 [SAR 47:48 DAR 9:16], 10033 kb/s, 30 fps, 30 tbr, 15360 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 h264_nvenc
  Stream #1:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 (h264) -> fps:default
  Stream #1:0 (h264) -> fps:default
  interleave:default -> Stream #0:0 (h264_nvenc)
Press [q] to stop, [?] for help
[Parsed_interleave_2 @ 0000021d15530600] Parameters for input link input1 (size 648x1128, SAR 47:48) do not match the corresponding output link parameters (648x1128, SAR 1:1)
[Parsed_interleave_2 @ 0000021d15530600] Failed to configure output pad on Parsed_interleave_2
[fc#0 @ 0000021d108d0800] Error reinitializing filters!
[fc#0 @ 0000021d108d0800] Task finished with error code: -22 (Invalid argument)
[fc#0 @ 0000021d108d0800] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 0000021d10f8bd00] Could not open encoder before EOF
[vost#0:0/h264_nvenc @ 0000021d10f8bd00] Task finished with error code: -22 (Invalid argument)
[vost#0:0/h264_nvenc @ 0000021d10f8bd00] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 0000021d11213d80] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!

2025-07-28 02:05:06 - VideoInterleaver - ERROR - 视频交错处理失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\normalized_0.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:01:06.30, start: 0.000000, bitrate: 10434 kb/s
  Stream #0:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(progressive), 1102x1920 [SAR 1:1 DAR 551:960], 10296 kb/s, 30 fps, 30 tbr, 15360 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 h264_nvenc
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 130 kb/s (default)
      Metadata:
        handler_name    : Core Media Audio
        vendor_id       : [0][0][0][0]
Input #1, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_19mhux0q\normalized_1.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:01:06.33, start: 0.000000, bitrate: 10186 kb/s
  Stream #1:0[0x1](eng): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(progressive), 1102x1920 [SAR 1:1 DAR 551:960], 10049 kb/s, 30 fps, 30 tbr, 15360 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 h264_nvenc
  Stream #1:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 129 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
[AVFormatContext @ 000002aba3d743c0] Unable to choose an output format for 'pipe:1'; use a standard extension for the filename or specify the format manually.
[out#0 @ 000002aba3e7b3c0] Error initializing the muxer for pipe:1: Invalid argument
Error opening output file pipe:1.
Error opening output files: Invalid argument

2025-07-28 02:05:06 - VideoInterleaver - ERROR - 输出文件未生成
2025-07-28 02:16:25 - VideoInterleaver - ERROR - 视频交错处理失败: ffmpeg version 7.0.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 13.2.0 (Rev5, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\normalized_0.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:00:14.86, start: 0.000000, bitrate: 11193 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 11057 kb/s, 36 fps, 36 tbr, 18432 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 127 kb/s (default)
      Metadata:
        handler_name    : Core Media Audio
        vendor_id       : [0][0][0][0]
Input #1, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\video_interleave_ged39hcn\normalized_1.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Duration: 00:00:14.89, start: 0.000000, bitrate: 8830 kb/s
  Stream #1:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1080x1920 [SAR 1:1 DAR 9:16], 8691 kb/s, 36 fps, 36 tbr, 18432 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
  Stream #1:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
[out#0/mp4 @ 00000147321deec0] Error opening output C:/Users/<USER>/Desktop/1.mp4: Permission denied
Error opening output file C:/Users/<USER>/Desktop/1.mp4.
Error opening output files: Permission denied

2025-07-28 02:16:25 - VideoInterleaver - ERROR - 输出文件未生成
2025-07-28 02:56:20 - root - ERROR - 应用程序运行时发生错误: name 'QApplication' is not defined
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\main.py", line 115, in main
    main_window = MainWindow()
                  ^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\7\video_interleave_project\gui\main_window.py", line 99, in __init__
    screen = QApplication.primaryScreen().geometry()
             ^^^^^^^^^^^^
NameError: name 'QApplication' is not defined
