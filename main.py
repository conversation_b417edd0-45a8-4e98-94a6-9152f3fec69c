#!/usr/bin/env python3
"""
视频交错隐藏技术主程序
"""

import sys
import os
import logging
from pathlib import Path
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui.main_window import MainWindow
from utils.logger import setup_logger

def setup_application():
    """设置应用程序"""
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("视频交错隐藏工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Video Interleave Team")

    # 设置应用程序属性（PyQt6兼容性修复）
    try:
        # PyQt6中高DPI缩放默认启用，这些属性可能不存在
        if hasattr(Qt.ApplicationAttribute, 'AA_EnableHighDpiScaling'):
            app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        if hasattr(Qt.ApplicationAttribute, 'AA_UseHighDpiPixmaps'):
            app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    except AttributeError:
        # PyQt6中这些属性可能已被移除，忽略错误
        pass

    return app

def setup_directories():
    """创建必要的目录"""
    directories = [
        "temp",
        "output", 
        "logs",
        "bin"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(exist_ok=True)

def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    # 检查Python包
    required_packages = [
        "PyQt6",
        "numpy", 
        "PIL",
        "cv2"
    ]
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_deps.append(package)
    
    # 检查FFmpeg
    ffmpeg_path = project_root / "bin" / "ffmpeg.exe"
    if not ffmpeg_path.exists():
        # 尝试系统PATH中的ffmpeg
        import subprocess
        try:
            subprocess.run(["ffmpeg", "-version"], 
                         capture_output=True, check=True, timeout=5)
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            missing_deps.append("ffmpeg")
    
    if missing_deps:
        print("❌ 缺少以下依赖项:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n请安装缺少的依赖项后重新运行程序")
        print("运行以下命令安装Python依赖:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖项检查通过")
    return True

def main():
    """主函数"""
    print("🚀 启动视频交错隐藏工具...")
    
    # 检查依赖项
    if not check_dependencies():
        sys.exit(1)
    
    # 创建必要目录
    setup_directories()
    
    # 设置日志
    logger = setup_logger()
    logger.info("应用程序启动")
    
    try:
        # 创建应用程序
        app = setup_application()
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        logger.info("主窗口已显示")
        
        # 运行应用程序
        exit_code = app.exec()
        
        logger.info(f"应用程序退出，退出码: {exit_code}")
        return exit_code
        
    except Exception as e:
        logger.error(f"应用程序运行时发生错误: {e}", exc_info=True)
        print(f"❌ 程序运行出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
