#!/usr/bin/env python3
"""
测试主程序 - 验证PyQt6导入和基本功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有必要的导入"""
    print("🔍 测试导入...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        print("   ✅ PyQt6.QtWidgets 导入成功")
    except ImportError as e:
        print(f"   ❌ PyQt6.QtWidgets 导入失败: {e}")
        return False
    
    try:
        from PyQt6.QtCore import Qt
        print("   ✅ PyQt6.QtCore 导入成功")
    except ImportError as e:
        print(f"   ❌ PyQt6.QtCore 导入失败: {e}")
        return False
    
    try:
        from gui.main_window import MainWindow
        print("   ✅ MainWindow 导入成功")
    except ImportError as e:
        print(f"   ❌ MainWindow 导入失败: {e}")
        return False
    
    try:
        from utils.logger import setup_logger
        print("   ✅ Logger 导入成功")
    except ImportError as e:
        print(f"   ❌ Logger 导入失败: {e}")
        return False
    
    return True

def test_ffmpeg():
    """测试FFmpeg可用性"""
    print("\n🔍 测试FFmpeg...")
    
    ffmpeg_path = project_root / "bin" / "ffmpeg.exe"
    if ffmpeg_path.exists():
        print("   ✅ FFmpeg 文件存在")
        return True
    else:
        print("   ❌ FFmpeg 文件不存在")
        return False

def main():
    """主函数"""
    print("🚀 测试视频交错隐藏工具")
    print("=" * 40)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败")
        input("按回车键退出...")
        return 1
    
    # 测试FFmpeg
    if not test_ffmpeg():
        print("\n⚠️ FFmpeg不可用，但程序仍可启动")
    
    print("\n🎬 启动图形界面...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from gui.main_window import MainWindow
        from utils.logger import setup_logger
        
        # 设置日志
        logger = setup_logger()
        logger.info("测试程序启动")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("视频交错隐藏工具 - 测试版")
        app.setApplicationVersion("1.0.0")
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        print("✅ 程序启动成功")
        print("\n💡 如果看到这条消息，说明PyQt6导入正常")
        print("   IDE显示的导入错误可以忽略")
        
        # 运行应用程序
        exit_code = app.exec()
        
        logger.info(f"应用程序退出，退出码: {exit_code}")
        return exit_code
        
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
