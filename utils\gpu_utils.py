"""
GPU加速工具模块
"""

import os
import subprocess
import logging
import platform
from typing import Dict, Any, List, Optional, Tuple

class GPUAccelerator:
    """GPU加速管理类"""
    
    def __init__(self, ffmpeg_path: str = "ffmpeg"):
        self.ffmpeg_path = ffmpeg_path
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # GPU编码器映射
        self.gpu_encoders = {
            "nvidia": {
                "h264": "h264_nvenc",
                "h265": "hevc_nvenc",
                "av1": "av1_nvenc"  # 较新的GPU支持
            },
            "amd": {
                "h264": "h264_amf",
                "h265": "hevc_amf"
            },
            "intel": {
                "h264": "h264_qsv",
                "h265": "hevc_qsv",
                "av1": "av1_qsv"
            }
        }
        
        # NVENC预设映射
        self.nvenc_presets = {
            "fastest": "p1",
            "faster": "p2", 
            "fast": "p3",
            "medium": "p4",
            "slow": "p5",
            "slower": "p6",
            "slowest": "p7"
        }
        
        # 检测可用的GPU
        self.available_gpus = self._detect_gpus()
        self.logger.info(f"检测到GPU: {list(self.available_gpus.keys())}")
    
    def _detect_gpus(self) -> Dict[str, Dict[str, Any]]:
        """检测可用的GPU"""
        gpus = {}
        
        # 检测NVIDIA GPU
        nvidia_info = self._detect_nvidia_gpu()
        if nvidia_info:
            gpus["nvidia"] = nvidia_info
        
        # 检测AMD GPU
        amd_info = self._detect_amd_gpu()
        if amd_info:
            gpus["amd"] = amd_info
            
        # 检测Intel GPU
        intel_info = self._detect_intel_gpu()
        if intel_info:
            gpus["intel"] = intel_info
        
        return gpus
    
    def _detect_nvidia_gpu(self) -> Optional[Dict[str, Any]]:
        """检测NVIDIA GPU"""
        try:
            # 尝试运行nvidia-smi
            result = subprocess.run(["nvidia-smi", "--query-gpu=name,memory.total,driver_version", 
                                   "--format=csv,noheader,nounits"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                gpu_info = lines[0].split(', ')
                
                if len(gpu_info) >= 3:
                    return {
                        "name": gpu_info[0].strip(),
                        "memory": f"{gpu_info[1].strip()} MB",
                        "driver": gpu_info[2].strip(),
                        "encoders": self._check_nvenc_support()
                    }
        except Exception as e:
            self.logger.debug(f"NVIDIA GPU检测失败: {e}")
        
        return None
    
    def _detect_amd_gpu(self) -> Optional[Dict[str, Any]]:
        """检测AMD GPU"""
        try:
            # Windows上检查AMD显卡
            if platform.system() == "Windows":
                result = subprocess.run(["wmic", "path", "win32_VideoController", 
                                       "get", "name", "/format:list"], 
                                      capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if 'Name=' in line and ('AMD' in line or 'Radeon' in line):
                            gpu_name = line.split('=')[1].strip()
                            return {
                                "name": gpu_name,
                                "encoders": self._check_amf_support()
                            }
        except Exception as e:
            self.logger.debug(f"AMD GPU检测失败: {e}")
        
        return None
    
    def _detect_intel_gpu(self) -> Optional[Dict[str, Any]]:
        """检测Intel GPU"""
        try:
            # Windows上检查Intel显卡
            if platform.system() == "Windows":
                result = subprocess.run(["wmic", "path", "win32_VideoController", 
                                       "get", "name", "/format:list"], 
                                      capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if 'Name=' in line and 'Intel' in line:
                            gpu_name = line.split('=')[1].strip()
                            return {
                                "name": gpu_name,
                                "encoders": self._check_qsv_support()
                            }
        except Exception as e:
            self.logger.debug(f"Intel GPU检测失败: {e}")
        
        return None
    
    def _check_nvenc_support(self) -> List[str]:
        """检查NVENC编码器支持"""
        supported = []
        encoders_to_check = ["h264_nvenc", "hevc_nvenc", "av1_nvenc"]
        
        for encoder in encoders_to_check:
            if self._test_encoder(encoder):
                supported.append(encoder)
        
        return supported
    
    def _check_amf_support(self) -> List[str]:
        """检查AMF编码器支持"""
        supported = []
        encoders_to_check = ["h264_amf", "hevc_amf"]
        
        for encoder in encoders_to_check:
            if self._test_encoder(encoder):
                supported.append(encoder)
        
        return supported
    
    def _check_qsv_support(self) -> List[str]:
        """检查QSV编码器支持"""
        supported = []
        encoders_to_check = ["h264_qsv", "hevc_qsv", "av1_qsv"]
        
        for encoder in encoders_to_check:
            if self._test_encoder(encoder):
                supported.append(encoder)
        
        return supported
    
    def _test_encoder(self, encoder: str) -> bool:
        """测试编码器是否可用"""
        try:
            # 首先检查编码器列表
            cmd = [self.ffmpeg_path, "-hide_banner", "-encoders"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if encoder not in result.stdout:
                self.logger.debug(f"编码器 {encoder} 不在FFmpeg编码器列表中")
                return False

            # 对于NVENC编码器，使用简化的测试
            if "nvenc" in encoder:
                # 简化的NVENC测试
                test_cmd = [
                    self.ffmpeg_path,
                    "-f", "lavfi",
                    "-i", "testsrc=duration=1:size=320x240:rate=1",
                    "-c:v", encoder,
                    "-preset", "fast",
                    "-frames:v", "1",
                    "-f", "null",
                    "-"
                ]
            else:
                # 其他编码器的标准测试
                test_cmd = [
                    self.ffmpeg_path,
                    "-f", "lavfi",
                    "-i", "testsrc=duration=1:size=320x240:rate=1",
                    "-c:v", encoder,
                    "-frames:v", "1",
                    "-f", "null",
                    "-"
                ]

            test_result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=15)
            success = test_result.returncode == 0

            if not success:
                self.logger.debug(f"编码器 {encoder} 测试失败: {test_result.stderr}")

            return success

        except Exception as e:
            self.logger.debug(f"编码器 {encoder} 测试异常: {e}")
            return False
    
    def is_gpu_available(self) -> bool:
        """检查是否有可用的GPU"""
        return len(self.available_gpus) > 0
    
    def get_best_gpu_encoder(self, codec: str) -> Optional[Tuple[str, str]]:
        """
        获取最佳的GPU编码器
        
        Args:
            codec: 编码格式 (h264, h265, av1)
            
        Returns:
            (gpu_type, encoder_name) 或 None
        """
        # 优先级: NVIDIA > AMD > Intel
        priority = ["nvidia", "amd", "intel"]
        
        for gpu_type in priority:
            if gpu_type in self.available_gpus:
                gpu_info = self.available_gpus[gpu_type]
                if codec in self.gpu_encoders[gpu_type]:
                    encoder = self.gpu_encoders[gpu_type][codec]
                    if encoder in gpu_info.get("encoders", []):
                        return (gpu_type, encoder)
        
        return None
    
    def build_gpu_encoding_params(self, codec: str, quality: str = "medium", 
                                 bitrate: Optional[str] = None) -> Dict[str, Any]:
        """
        构建GPU编码参数
        
        Args:
            codec: 编码格式
            quality: 质量预设
            bitrate: 码率设置
            
        Returns:
            编码参数字典
        """
        gpu_encoder = self.get_best_gpu_encoder(codec)
        if not gpu_encoder:
            return {}
        
        gpu_type, encoder = gpu_encoder
        params = {"codec": encoder}
        
        if gpu_type == "nvidia":
            # NVENC参数 - 参考video_interleave_hiding.py的正确实现
            if bitrate:
                # 使用码率控制模式
                params.update({
                    "preset": self.nvenc_presets.get(quality, "p4"),
                    "rc": "vbr",  # 使用VBR而不是CQ
                    "b:v": bitrate,
                    "maxrate": bitrate,
                    "bufsize": f"{int(bitrate.rstrip('k')) * 2}k"
                })
            else:
                # 使用恒定质量模式 - 正确的CQ参数
                cq_value = self._get_nvenc_cq(quality)
                params.update({
                    "preset": self.nvenc_presets.get(quality, "p4"),
                    "cq": str(cq_value),  # 正确的CQ参数
                    "rc": "vbr"  # 使用VBR模式
                })
                
        elif gpu_type == "amd":
            # AMF参数
            params.update({
                "quality": quality,
                "rc": "vbr" if bitrate else "cqp",
                "qp_i": self._get_amf_qp(quality),
                "qp_p": self._get_amf_qp(quality),
                "qp_b": self._get_amf_qp(quality)
            })
            
            if bitrate:
                params["b:v"] = bitrate
                
        elif gpu_type == "intel":
            # QSV参数
            params.update({
                "preset": quality,
                "global_quality": self._get_qsv_quality(quality)
            })
            
            if bitrate:
                params["b:v"] = bitrate
        
        return params
    
    def _get_nvenc_cq(self, quality: str) -> int:
        """获取NVENC CQ值"""
        cq_map = {
            "fastest": 28,
            "faster": 25,
            "fast": 23,
            "medium": 20,
            "slow": 18,
            "slower": 16,
            "slowest": 14
        }
        return cq_map.get(quality, 20)
    
    def _get_amf_qp(self, quality: str) -> int:
        """获取AMF QP值"""
        qp_map = {
            "fastest": 28,
            "faster": 25,
            "fast": 23,
            "medium": 20,
            "slow": 18,
            "slower": 16,
            "slowest": 14
        }
        return qp_map.get(quality, 20)
    
    def _get_qsv_quality(self, quality: str) -> int:
        """获取QSV质量值"""
        quality_map = {
            "fastest": 28,
            "faster": 25,
            "fast": 23,
            "medium": 20,
            "slow": 18,
            "slower": 16,
            "slowest": 14
        }
        return quality_map.get(quality, 20)
    
    def get_gpu_info_summary(self) -> str:
        """获取GPU信息摘要"""
        if not self.available_gpus:
            return "未检测到可用的GPU"
        
        summary = []
        for gpu_type, info in self.available_gpus.items():
            gpu_name = info.get("name", f"{gpu_type.upper()} GPU")
            encoders = info.get("encoders", [])
            summary.append(f"{gpu_name}: {', '.join(encoders) if encoders else '无可用编码器'}")
        
        return "; ".join(summary)
