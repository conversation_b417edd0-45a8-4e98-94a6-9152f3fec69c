"""
日志工具模块
"""

import logging
import logging.handlers
import os
from pathlib import Path
from datetime import datetime

def setup_logger(name: str = None, level: str = "INFO") -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称，如果为None则使用根记录器
        level: 日志级别
        
    Returns:
        配置好的日志记录器
    """
    # 创建日志目录
    log_dir = Path(__file__).parent.parent / "logs"
    log_dir.mkdir(exist_ok=True)
    
    # 获取日志记录器
    logger = logging.getLogger(name)
    
    # 如果已经配置过，直接返回
    if logger.handlers:
        return logger
    
    # 设置日志级别
    log_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(log_level)
    
    # 创建格式化器
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器 - 按日期轮转
    today = datetime.now().strftime("%Y%m%d")
    log_file = log_dir / f"video_interleave_{today}.log"
    
    file_handler = logging.handlers.RotatingFileHandler(
        filename=log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 错误日志文件处理器
    error_log_file = log_dir / f"video_interleave_error_{today}.log"
    error_handler = logging.handlers.RotatingFileHandler(
        filename=error_log_file,
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    logger.addHandler(error_handler)
    
    logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    return logger

def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器
    """
    return logging.getLogger(name)

class LogCapture:
    """日志捕获器，用于在GUI中显示日志"""
    
    def __init__(self, callback=None):
        self.callback = callback
        self.logs = []
        
    def write(self, message):
        """写入日志消息"""
        if message.strip():
            self.logs.append(message.strip())
            if self.callback:
                self.callback(message.strip())
    
    def flush(self):
        """刷新缓冲区"""
        pass
    
    def get_logs(self):
        """获取所有日志"""
        return self.logs.copy()
    
    def clear(self):
        """清空日志"""
        self.logs.clear()

class GuiLogHandler(logging.Handler):
    """GUI日志处理器"""
    
    def __init__(self, callback=None):
        super().__init__()
        self.callback = callback
        
    def emit(self, record):
        """发送日志记录"""
        if self.callback:
            try:
                msg = self.format(record)
                self.callback(msg)
            except Exception:
                self.handleError(record)
