"""
视频处理工具模块
"""

import os
import subprocess
import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

class VideoUtils:
    """视频处理工具类"""
    
    def __init__(self, ffmpeg_path: str = "ffmpeg", ffprobe_path: str = "ffprobe"):
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        获取视频基本信息 - 参考video_interleave_hiding.py的简洁实现

        Args:
            video_path: 视频文件路径

        Returns:
            视频信息字典
        """
        try:
            cmd = [
                self.ffprobe_path,
                "-v", "error",
                "-select_streams", "v:0",
                "-show_entries", "stream=width,height,avg_frame_rate,duration",
                "-of", "json",
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True,
                                  check=True, timeout=30)
            info = json.loads(result.stdout)
            stream = info.get("streams", [{}])[0]

            # 计算帧率
            frame_rate_str = stream.get("avg_frame_rate", "0/1")
            if '/' in frame_rate_str:
                num, den = frame_rate_str.split('/')
                frame_rate = float(num) / float(den) if float(den) != 0 else 0
            else:
                frame_rate = float(frame_rate_str)

            return {
                "width": stream.get("width", 0),
                "height": stream.get("height", 0),
                "frame_rate": frame_rate,
                "duration": float(stream.get("duration", 0)),
                "resolution": f"{stream.get('width', 0)}x{stream.get('height', 0)}"
            }
            
            if not video_stream:
                raise ValueError("未找到视频流")
            
            # 解析帧率
            fps_str = video_stream.get("r_frame_rate", "30/1")
            try:
                if "/" in fps_str:
                    num, den = fps_str.split("/")
                    fps = float(num) / float(den)
                else:
                    fps = float(fps_str)
            except:
                fps = 30.0
            
            # 构建返回信息
            info = {
                "format": {
                    "filename": data["format"]["filename"],
                    "format_name": data["format"]["format_name"],
                    "duration": float(data["format"]["duration"]),
                    "size": int(data["format"]["size"]),
                    "bit_rate": int(data["format"].get("bit_rate", 0))
                },
                "video": {
                    "codec": video_stream["codec_name"],
                    "width": int(video_stream["width"]),
                    "height": int(video_stream["height"]),
                    "fps": fps,
                    "bit_rate": int(video_stream.get("bit_rate", 0)),
                    "pixel_format": video_stream.get("pix_fmt", "unknown")
                },
                "audio": []
            }
            
            # 添加音频流信息
            for audio_stream in audio_streams:
                audio_info = {
                    "codec": audio_stream["codec_name"],
                    "sample_rate": int(audio_stream.get("sample_rate", 0)),
                    "channels": int(audio_stream.get("channels", 0)),
                    "bit_rate": int(audio_stream.get("bit_rate", 0))
                }
                info["audio"].append(audio_info)
            
            return info
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"FFprobe执行失败: {e.stderr}")
            raise
        except Exception as e:
            self.logger.error(f"获取视频信息失败: {e}")
            raise
    
    def get_video_duration(self, video_path: str) -> float:
        """
        获取视频时长
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            视频时长（秒）
        """
        try:
            info = self.get_video_info(video_path)
            return info["format"]["duration"]
        except Exception as e:
            self.logger.error(f"获取视频时长失败: {e}")
            return 0.0
    
    def get_video_resolution(self, video_path: str) -> Tuple[int, int]:
        """
        获取视频分辨率
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            (宽度, 高度) 元组
        """
        try:
            info = self.get_video_info(video_path)
            return (info["video"]["width"], info["video"]["height"])
        except Exception as e:
            self.logger.error(f"获取视频分辨率失败: {e}")
            return (0, 0)
    
    def get_video_fps(self, video_path: str) -> float:
        """
        获取视频帧率
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            视频帧率
        """
        try:
            info = self.get_video_info(video_path)
            return info["video"]["fps"]
        except Exception as e:
            self.logger.error(f"获取视频帧率失败: {e}")
            return 0.0
    
    def check_video_compatibility(self, video_paths: List[str]) -> Dict[str, Any]:
        """
        检查多个视频的兼容性
        
        Args:
            video_paths: 视频文件路径列表
            
        Returns:
            兼容性检查结果
        """
        if not video_paths:
            return {"compatible": False, "error": "视频路径列表为空"}
        
        try:
            # 获取所有视频信息
            video_infos = []
            for path in video_paths:
                if not os.path.exists(path):
                    return {"compatible": False, "error": f"文件不存在: {path}"}
                
                info = self.get_video_info(path)
                video_infos.append(info)
            
            # 检查兼容性
            reference = video_infos[0]["video"]
            issues = []
            
            for i, info in enumerate(video_infos[1:], 1):
                video = info["video"]
                
                # 检查分辨率
                if video["width"] != reference["width"] or video["height"] != reference["height"]:
                    issues.append(f"视频{i+1}分辨率不匹配: {video['width']}x{video['height']} vs {reference['width']}x{reference['height']}")
                
                # 检查帧率（允许小幅差异）
                if abs(video["fps"] - reference["fps"]) > 0.1:
                    issues.append(f"视频{i+1}帧率不匹配: {video['fps']:.2f} vs {reference['fps']:.2f}")
            
            return {
                "compatible": len(issues) == 0,
                "issues": issues,
                "reference_resolution": f"{reference['width']}x{reference['height']}",
                "reference_fps": reference["fps"],
                "video_count": len(video_infos)
            }
            
        except Exception as e:
            return {"compatible": False, "error": str(e)}
    
    def extract_video_frames(self, video_path: str, output_dir: str, 
                           frame_rate: Optional[float] = None) -> bool:
        """
        提取视频帧
        
        Args:
            video_path: 视频文件路径
            output_dir: 输出目录
            frame_rate: 提取帧率，None表示提取所有帧
            
        Returns:
            是否成功
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            cmd = [self.ffmpeg_path, "-i", video_path]
            
            if frame_rate:
                cmd.extend(["-vf", f"fps={frame_rate}"])
            
            cmd.extend([
                "-y",  # 覆盖输出文件
                os.path.join(output_dir, "frame_%04d.png")
            ])
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            self.logger.info(f"帧提取完成: {output_dir}")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"帧提取失败: {e.stderr}")
            return False
        except Exception as e:
            self.logger.error(f"帧提取异常: {e}")
            return False
    
    def create_video_from_frames(self, frames_dir: str, output_path: str, 
                                fps: float = 30.0) -> bool:
        """
        从帧序列创建视频
        
        Args:
            frames_dir: 帧序列目录
            output_path: 输出视频路径
            fps: 输出帧率
            
        Returns:
            是否成功
        """
        try:
            cmd = [
                self.ffmpeg_path,
                "-framerate", str(fps),
                "-i", os.path.join(frames_dir, "frame_%04d.png"),
                "-c:v", "libx264",
                "-pix_fmt", "yuv420p",
                "-y",
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            self.logger.info(f"视频创建完成: {output_path}")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"视频创建失败: {e.stderr}")
            return False
        except Exception as e:
            self.logger.error(f"视频创建异常: {e}")
            return False
    
    def convert_video_format(self, input_path: str, output_path: str, 
                           codec: str = "libx264", quality: int = 23) -> bool:
        """
        转换视频格式
        
        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            codec: 视频编码器
            quality: 质量参数（CRF值）
            
        Returns:
            是否成功
        """
        try:
            cmd = [
                self.ffmpeg_path,
                "-i", input_path,
                "-c:v", codec,
                "-crf", str(quality),
                "-c:a", "aac",
                "-y",
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            self.logger.info(f"格式转换完成: {output_path}")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"格式转换失败: {e.stderr}")
            return False
        except Exception as e:
            self.logger.error(f"格式转换异常: {e}")
            return False
    
    @staticmethod
    def get_supported_formats() -> Dict[str, List[str]]:
        """
        获取支持的视频格式
        
        Returns:
            支持的格式字典
        """
        return {
            "input": [
                ".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv",
                ".webm", ".m4v", ".3gp", ".ogv", ".ts", ".mts",
                ".m2ts", ".vob", ".asf", ".rm", ".rmvb"
            ],
            "output": [
                ".mp4", ".avi", ".mov", ".mkv", ".webm", ".m4v"
            ]
        }
    
    @staticmethod
    def is_video_file(file_path: str) -> bool:
        """
        检查是否为视频文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为视频文件
        """
        ext = os.path.splitext(file_path)[1].lower()
        supported = VideoUtils.get_supported_formats()
        return ext in supported["input"]
